"""
جامع البيانات المباشرة
جمع البيانات المباشرة من منصة Pocket Option
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable
from datetime import datetime

from .pocket_option_connector import PocketOptionConnector

logger = logging.getLogger(__name__)

class LiveDataCollector:
    """جامع البيانات المباشرة"""
    
    def __init__(self, account_type: str = 'demo'):
        self.account_type = account_type
        self.connector = PocketOptionConnector(account_type)
        self.is_running = False
        self.callbacks: List[Callable] = []
        
        logger.info(f"تم إنشاء جامع البيانات المباشرة - نوع الحساب: {account_type}")
    
    async def start(self):
        """بدء جمع البيانات المباشرة"""
        logger.info("بدء جمع البيانات المباشرة...")
        
        if await self.connector.connect():
            self.is_running = True
            logger.info("✅ تم بدء جمع البيانات المباشرة")
            return True
        else:
            logger.error("❌ فشل في بدء جمع البيانات المباشرة")
            return False
    
    async def stop(self):
        """إيقاف جمع البيانات المباشرة"""
        logger.info("إيقاف جمع البيانات المباشرة...")
        self.is_running = False
        await self.connector.disconnect()
        logger.info("✅ تم إيقاف جمع البيانات المباشرة")
    
    def add_callback(self, callback: Callable):
        """إضافة callback للبيانات الجديدة"""
        self.callbacks.append(callback)
    
    async def get_live_data(self) -> Dict:
        """الحصول على البيانات المباشرة"""
        if not self.is_running:
            return {}
        
        try:
            balance = await self.connector.get_balance()
            payouts = await self.connector.update_payouts()
            
            return {
                'balance': balance,
                'payouts': payouts,
                'timestamp': datetime.now().isoformat(),
                'account_type': self.account_type
            }
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات المباشرة: {e}")
            return {}
