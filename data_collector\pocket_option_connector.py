"""
موصل Pocket Option
الاتصال غير المتزامن مع منصة Pocket Option
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from BinaryOptionsToolsV2.pocketoption import PocketOption
from config.settings import settings
from config.constants import AccountType

logger = logging.getLogger(__name__)

class PocketOptionConnector:
    """موصل Pocket Option غير المتزامن"""
    
    def __init__(self, account_type: str = 'demo'):
        self.account_type = account_type.lower()
        self.ssid = settings.get_ssid(self.account_type)
        self.api: Optional[PocketOption] = None
        self.is_connected = False
        self.connection_attempts = 0
        self.max_retries = settings.MAX_RETRIES
        
        # معلومات الحساب
        self.balance = 0.0
        self.payouts: Dict[str, float] = {}
        
        logger.info(f"تم إنشاء موصل Pocket Option - نوع الحساب: {self.account_type}")
    
    async def connect(self) -> bool:
        """الاتصال بمنصة Pocket Option"""
        try:
            logger.info(f"محاولة الاتصال بـ Pocket Option ({self.account_type})...")
            
            # إنشاء مثيل API
            self.api = PocketOption(self.ssid)

            # انتظار تأسيس الاتصال
            await asyncio.sleep(5)

            # اختبار الاتصال بجلب الرصيد (تشغيل في executor)
            loop = asyncio.get_event_loop()
            self.balance = await loop.run_in_executor(None, self.api.balance)
            
            if self.balance is not None:
                self.is_connected = True
                self.connection_attempts = 0
                logger.info(f"✅ تم الاتصال بنجاح - الرصيد: ${self.balance}")
                
                # جلب نسب الأرباح
                loop = asyncio.get_event_loop()
                self.payouts = await loop.run_in_executor(None, self.api.payout)
                
                return True
            else:
                raise Exception("فشل في جلب الرصيد")
                
        except Exception as e:
            self.connection_attempts += 1
            logger.error(f"❌ فشل الاتصال (المحاولة {self.connection_attempts}): {e}")
            
            if self.connection_attempts < self.max_retries:
                logger.info(f"إعادة المحاولة خلال {settings.RECONNECT_TIME} ثانية...")
                await asyncio.sleep(settings.RECONNECT_TIME)
                return await self.connect()
            else:
                logger.error("❌ فشل في الاتصال بعد عدة محاولات")
                return False
    
    async def disconnect(self):
        """قطع الاتصال"""
        if self.api:
            try:
                # إغلاق الاتصال إذا كان متاحاً
                if hasattr(self.api, 'close'):
                    await self.api.close()
                logger.info("✅ تم قطع الاتصال بنجاح")
            except Exception as e:
                logger.warning(f"تحذير أثناء قطع الاتصال: {e}")
            finally:
                self.is_connected = False
                self.api = None
    
    async def reconnect(self) -> bool:
        """إعادة الاتصال"""
        logger.info("محاولة إعادة الاتصال...")
        await self.disconnect()
        return await self.connect()
    
    async def ensure_connection(self) -> bool:
        """التأكد من وجود اتصال صالح"""
        if not self.is_connected or not self.api:
            return await self.connect()
        
        try:
            # اختبار الاتصال بجلب الرصيد
            loop = asyncio.get_event_loop()
            test_balance = await loop.run_in_executor(None, self.api.balance)
            if test_balance is not None:
                self.balance = test_balance
                return True
            else:
                return await self.reconnect()
        except Exception as e:
            logger.warning(f"مشكلة في الاتصال: {e}")
            return await self.reconnect()
    
    async def get_balance(self) -> float:
        """الحصول على الرصيد الحالي"""
        if await self.ensure_connection():
            try:
                loop = asyncio.get_event_loop()
                self.balance = await loop.run_in_executor(None, self.api.balance)
                return self.balance
            except Exception as e:
                logger.error(f"خطأ في جلب الرصيد: {e}")
                return 0.0
        return 0.0
    
    async def update_payouts(self) -> Dict[str, float]:
        """تحديث نسب الأرباح"""
        if await self.ensure_connection():
            try:
                loop = asyncio.get_event_loop()
                self.payouts = await loop.run_in_executor(None, self.api.payout)
                logger.info(f"تم تحديث نسب الأرباح - عدد الأصول: {len(self.payouts)}")
                return self.payouts
            except Exception as e:
                logger.error(f"خطأ في جلب نسب الأرباح: {e}")
                return {}
        return {}
    
    async def get_payout(self, symbol: str) -> float:
        """الحصول على نسبة الربح لزوج معين"""
        if not self.payouts:
            await self.update_payouts()
        
        return self.payouts.get(symbol, 0.0)
    
    async def get_candles(self, symbol: str, timeframe: int, count: int) -> List[Dict]:
        """جلب البيانات التاريخية"""
        if await self.ensure_connection():
            try:
                candles = await self.api.get_candles(symbol, timeframe, count)
                logger.debug(f"تم جلب {len(candles)} شمعة لـ {symbol}")
                return candles
            except Exception as e:
                logger.error(f"خطأ في جلب البيانات التاريخية لـ {symbol}: {e}")
                return []
        return []
    
    async def place_trade(self, symbol: str, direction: str, amount: float, duration: int) -> Optional[str]:
        """وضع صفقة جديدة"""
        if await self.ensure_connection():
            try:
                # تحويل الاتجاه
                action = "call" if direction.lower() == "call" else "put"
                
                # وضع الصفقة
                result = await self.api.trade(action, amount, symbol, duration)
                
                if result and 'id' in result:
                    trade_id = result['id']
                    logger.info(f"✅ تم وضع صفقة {action} على {symbol} بمبلغ ${amount} - ID: {trade_id}")
                    return trade_id
                else:
                    logger.error(f"❌ فشل في وضع الصفقة: {result}")
                    return None
                    
            except Exception as e:
                logger.error(f"خطأ في وضع الصفقة: {e}")
                return None
        return None
    
    async def get_trade_result(self, trade_id: str) -> Optional[Dict]:
        """الحصول على نتيجة صفقة"""
        if await self.ensure_connection():
            try:
                # جلب معلومات الصفقة
                result = await self.api.get_trade_info(trade_id)
                return result
            except Exception as e:
                logger.error(f"خطأ في جلب نتيجة الصفقة {trade_id}: {e}")
                return None
        return None
    
    def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال"""
        return {
            'account_type': self.account_type,
            'is_connected': self.is_connected,
            'balance': self.balance,
            'payouts_count': len(self.payouts),
            'connection_attempts': self.connection_attempts,
            'last_update': datetime.now().isoformat()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """فحص صحة الاتصال"""
        health_info = {
            'status': 'unknown',
            'balance': 0.0,
            'payouts_available': 0,
            'response_time': 0.0,
            'error': None
        }
        
        try:
            start_time = datetime.now()
            
            # اختبار جلب الرصيد
            balance = await self.get_balance()
            
            # حساب وقت الاستجابة
            response_time = (datetime.now() - start_time).total_seconds()
            
            health_info.update({
                'status': 'healthy' if balance > 0 else 'warning',
                'balance': balance,
                'payouts_available': len(self.payouts),
                'response_time': response_time
            })
            
        except Exception as e:
            health_info.update({
                'status': 'error',
                'error': str(e)
            })
        
        return health_info
