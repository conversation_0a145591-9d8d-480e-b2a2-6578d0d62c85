"""
إعدادات النظام الرئيسية
قراءة متغيرات البيئة وإدارة التكوين
"""

import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class Settings:
    """فئة إعدادات النظام"""
    
    # ==========================================
    # 🔵 إعدادات PostgreSQL
    # ==========================================
    DB_HOST: str = os.getenv('DB_HOST', 'localhost')
    DB_PORT: int = int(os.getenv('DB_PORT', '5432'))
    DB_NAME: str = os.getenv('DB_NAME', 'scalping_trading')
    DB_USER: str = os.getenv('DB_USER', 'postgres')
    DB_PASSWORD: str = os.getenv('DB_PASSWORD', '8576')
    
    @property
    def DATABASE_URL(self) -> str:
        """رابط قاعدة البيانات الكامل"""
        return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    # ==========================================
    # 🔴 إعدادات Redis
    # ==========================================
    REDIS_HOST: str = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT: int = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_DB: int = int(os.getenv('REDIS_DB', '0'))
    REDIS_PASSWORD: str = os.getenv('REDIS_PASSWORD', '')
    
    @property
    def REDIS_CONFIG(self) -> Dict[str, Any]:
        """إعدادات Redis"""
        config = {
            'host': self.REDIS_HOST,
            'port': self.REDIS_PORT,
            'db': self.REDIS_DB,
            'decode_responses': True
        }
        if self.REDIS_PASSWORD:
            config['password'] = self.REDIS_PASSWORD
        return config
    
    # ==========================================
    # 🟡 إعدادات Pocket Option
    # ==========================================
    DEMO_SSID: str = os.getenv('DEMO_SSID', '')
    REAL_SSID: str = os.getenv('REAL_SSID', '')
    
    # ==========================================
    # ⚙️ إعدادات النظام العامة
    # ==========================================
    TIMEFRAME: int = int(os.getenv('TIMEFRAME', '300'))  # 5 دقائق
    MAX_CANDLES_PER_PAIR: int = int(os.getenv('MAX_CANDLES_PER_PAIR', '149'))
    CONNECTION_TIMEOUT: int = int(os.getenv('CONNECTION_TIMEOUT', '60'))
    RECONNECT_TIME: int = int(os.getenv('RECONNECT_TIME', '10'))
    MAX_RETRIES: int = int(os.getenv('MAX_RETRIES', '3'))
    
    # ==========================================
    # 📊 إعدادات المؤشرات الفنية
    # ==========================================
    INDICATORS_ENABLED: List[str] = os.getenv('INDICATORS_ENABLED', '').split(',')
    
    # ==========================================
    # 🔄 إعدادات المعالجة المتوازية
    # ==========================================
    TOTAL_PAIRS: int = int(os.getenv('TOTAL_PAIRS', '70'))
    MAX_CONCURRENT_CONNECTIONS: int = int(os.getenv('MAX_CONCURRENT_CONNECTIONS', '70'))
    
    # ==========================================
    # 📝 إعدادات السجلات
    # ==========================================
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE_PATH: str = os.getenv('LOG_FILE_PATH', 'logs/scalping_system.log')
    
    # ==========================================
    # 📈 قائمة الأزواج المستهدفة
    # ==========================================
    @property
    def CURRENCY_PAIRS(self) -> List[str]:
        """قائمة الأزواج الـ70"""
        pairs_str = os.getenv('CURRENCY_PAIRS', '')
        if pairs_str:
            return [pair.strip() for pair in pairs_str.split(',')]
        return []
    
    # ==========================================
    # 🔧 دوال مساعدة
    # ==========================================
    def get_ssid(self, account_type: str = 'demo') -> str:
        """الحصول على SSID حسب نوع الحساب"""
        if account_type.lower() == 'demo':
            return self.DEMO_SSID
        elif account_type.lower() == 'real':
            return self.REAL_SSID
        else:
            raise ValueError(f"نوع حساب غير صحيح: {account_type}")
    
    def validate_settings(self) -> bool:
        """التحقق من صحة الإعدادات"""
        errors = []
        
        # التحقق من إعدادات قاعدة البيانات
        if not self.DB_HOST or not self.DB_NAME or not self.DB_USER:
            errors.append("إعدادات PostgreSQL غير مكتملة")
        
        # التحقق من إعدادات Redis
        if not self.REDIS_HOST:
            errors.append("إعدادات Redis غير مكتملة")
        
        # التحقق من SSID
        if not self.DEMO_SSID and not self.REAL_SSID:
            errors.append("لا يوجد SSID صالح")
        
        # التحقق من قائمة الأزواج
        if len(self.CURRENCY_PAIRS) != self.TOTAL_PAIRS:
            errors.append(f"عدد الأزواج غير صحيح: {len(self.CURRENCY_PAIRS)} بدلاً من {self.TOTAL_PAIRS}")
        
        if errors:
            print("❌ أخطاء في الإعدادات:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        print("✅ جميع الإعدادات صحيحة")
        return True

# إنشاء مثيل الإعدادات
settings = Settings()

# التحقق من الإعدادات عند الاستيراد
if __name__ == "__main__":
    settings.validate_settings()
