"""
مستودعات البيانات
فئات للتعامل مع قاعدة البيانات
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

from .models import CurrencyPair, Candle, TechnicalIndicator, Trade, RiskSetting, SystemLog
from config.database import get_db_session

class BaseRepository:
    """الفئة الأساسية لجميع المستودعات"""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session or get_db_session()
    
    def commit(self):
        """حفظ التغييرات"""
        self.session.commit()
    
    def rollback(self):
        """التراجع عن التغييرات"""
        self.session.rollback()
    
    def close(self):
        """إغلاق الجلسة"""
        self.session.close()

class CurrencyPairRepository(BaseRepository):
    """مستودع أزواج العملات"""
    
    def get_all_pairs(self) -> List[CurrencyPair]:
        """الحصول على جميع أزواج العملات"""
        return self.session.query(CurrencyPair).all()
    
    def get_active_pairs(self) -> List[CurrencyPair]:
        """الحصول على الأزواج النشطة فقط"""
        return self.session.query(CurrencyPair).filter(CurrencyPair.is_active == True).all()
    
    def get_pair_by_symbol(self, symbol: str) -> Optional[CurrencyPair]:
        """الحصول على زوج بالرمز"""
        return self.session.query(CurrencyPair).filter(CurrencyPair.symbol == symbol).first()
    
    def get_pairs_by_category(self, category: str) -> List[CurrencyPair]:
        """الحصول على الأزواج حسب الفئة"""
        return self.session.query(CurrencyPair).filter(CurrencyPair.category == category).all()

class CandleRepository(BaseRepository):
    """مستودع الشموع"""
    
    def add_candle(self, candle: Candle) -> Candle:
        """إضافة شمعة جديدة"""
        self.session.add(candle)
        return candle
    
    def get_latest_candles(self, pair_id: int, limit: int = 149) -> List[Candle]:
        """الحصول على آخر شموع لزوج معين"""
        return (self.session.query(Candle)
                .filter(Candle.currency_pair_id == pair_id)
                .order_by(desc(Candle.timestamp))
                .limit(limit)
                .all())
    
    def get_candles_by_timerange(self, pair_id: int, start_time: datetime, end_time: datetime) -> List[Candle]:
        """الحصول على الشموع في فترة زمنية محددة"""
        return (self.session.query(Candle)
                .filter(and_(
                    Candle.currency_pair_id == pair_id,
                    Candle.timestamp >= start_time,
                    Candle.timestamp <= end_time
                ))
                .order_by(asc(Candle.timestamp))
                .all())

class TechnicalIndicatorRepository(BaseRepository):
    """مستودع المؤشرات الفنية"""
    
    def add_indicator(self, indicator: TechnicalIndicator) -> TechnicalIndicator:
        """إضافة مؤشر فني جديد"""
        self.session.add(indicator)
        return indicator
    
    def get_latest_indicators(self, pair_id: int, indicator_name: str, limit: int = 50) -> List[TechnicalIndicator]:
        """الحصول على آخر قيم مؤشر معين"""
        return (self.session.query(TechnicalIndicator)
                .filter(and_(
                    TechnicalIndicator.currency_pair_id == pair_id,
                    TechnicalIndicator.indicator_name == indicator_name
                ))
                .order_by(desc(TechnicalIndicator.timestamp))
                .limit(limit)
                .all())

class TradeRepository(BaseRepository):
    """مستودع الصفقات"""
    
    def add_trade(self, trade: Trade) -> Trade:
        """إضافة صفقة جديدة"""
        self.session.add(trade)
        return trade
    
    def get_active_trades(self, account_type: str = 'demo') -> List[Trade]:
        """الحصول على الصفقات النشطة"""
        return (self.session.query(Trade)
                .filter(and_(
                    Trade.account_type == account_type,
                    Trade.status == 'pending'
                ))
                .all())

class RiskSettingRepository(BaseRepository):
    """مستودع إعدادات المخاطر"""
    
    def get_all_settings(self) -> List[RiskSetting]:
        """الحصول على جميع إعدادات المخاطر"""
        return self.session.query(RiskSetting).filter(RiskSetting.is_active == True).all()
    
    def get_setting_by_name(self, setting_name: str) -> Optional[RiskSetting]:
        """الحصول على إعداد بالاسم"""
        return self.session.query(RiskSetting).filter(RiskSetting.setting_name == setting_name).first()
