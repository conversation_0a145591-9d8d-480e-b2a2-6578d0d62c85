#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصالات الأساسية للنظام
- PostgreSQL
- Redis  
- Pocket Option (الحساب التجريبي والحقيقي)
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# استيراد المكتبات المطلوبة
import psycopg2
import redis
from BinaryOptionsToolsV2.pocketoption import PocketOption, PocketOptionAsync

# بيانات الاتصال من SYSTEM_ARCHITECTURE.md
DEMO_SSID = '42["auth",{"session":"sblkni7rg1bo7pqipkqb9mcv66","isDemo":1,"uid":99087051,"platform":2,"isFastHistory":true}]'
REAL_SSID = '42["auth",{"session":"a:4:{s:10:\\"session_id\\";s:32:\\"866de092f3f3be07766382237cd7882a\\";s:10:\\"ip_address\\";s:14:\\"**************\\";s:10:\\"user_agent\\";s:111:\\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\\";s:13:\\"last_activity\\";i:1750644298;}03f4c73c659cf2c5979b629b118ffe92","isDemo":0,"uid":99087051,"platform":2,"isFastHistory":true}]'

# إعدادات قواعد البيانات الافتراضية
POSTGRES_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'scalping_trading_db',
    'user': 'postgres',
    'password': 'postgres'
}

REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0,
    'decode_responses': True
}

class ConnectionTester:
    """فئة اختبار الاتصالات"""
    
    def __init__(self):
        self.results = {
            'postgresql': False,
            'redis': False,
            'pocket_option_demo': False,
            'pocket_option_real': False
        }
        self.errors = []
    
    def test_postgresql(self):
        """اختبار الاتصال بـ PostgreSQL"""
        print("🔵 اختبار الاتصال بـ PostgreSQL...")
        try:
            conn = psycopg2.connect(**POSTGRES_CONFIG)
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"✅ PostgreSQL متصل بنجاح: {version[0][:50]}...")
            
            # اختبار إنشاء جدول بسيط
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_connection (
                    id SERIAL PRIMARY KEY,
                    test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            cursor.execute("INSERT INTO test_connection DEFAULT VALUES;")
            conn.commit()
            
            cursor.execute("SELECT COUNT(*) FROM test_connection;")
            count = cursor.fetchone()[0]
            print(f"✅ اختبار الكتابة نجح: {count} سجل في الجدول")
            
            # تنظيف
            cursor.execute("DROP TABLE test_connection;")
            conn.commit()
            
            cursor.close()
            conn.close()
            self.results['postgresql'] = True
            return True
            
        except Exception as e:
            error_msg = f"❌ فشل الاتصال بـ PostgreSQL: {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_redis(self):
        """اختبار الاتصال بـ Redis"""
        print("🔴 اختبار الاتصال بـ Redis...")
        try:
            r = redis.Redis(**REDIS_CONFIG)
            
            # اختبار ping
            if r.ping():
                print("✅ Redis متصل بنجاح")
                
                # اختبار الكتابة والقراءة
                test_key = f"test_connection_{int(time.time())}"
                test_value = "اختبار الاتصال"
                
                r.set(test_key, test_value, ex=60)  # انتهاء صلاحية بعد دقيقة
                retrieved_value = r.get(test_key)
                
                if retrieved_value == test_value:
                    print("✅ اختبار الكتابة والقراءة نجح")
                    r.delete(test_key)  # تنظيف
                    self.results['redis'] = True
                    return True
                else:
                    raise Exception("فشل في قراءة البيانات المكتوبة")
            else:
                raise Exception("فشل في ping")
                
        except Exception as e:
            error_msg = f"❌ فشل الاتصال بـ Redis: {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_pocket_option_demo(self):
        """اختبار الاتصال بـ Pocket Option - الحساب التجريبي"""
        print("🟡 اختبار الاتصال بـ Pocket Option (الحساب التجريبي)...")
        try:
            api = PocketOption(DEMO_SSID)
            time.sleep(5)  # انتظار تأسيس الاتصال
            
            # اختبار جلب الرصيد
            balance = api.balance()
            print(f"✅ الرصيد التجريبي: ${balance}")
            
            # اختبار جلب نسب الأرباح
            payouts = api.payout()
            print(f"✅ عدد الأصول المتاحة: {len(payouts)}")
            
            # اختبار جلب البيانات التاريخية لزوج واحد
            candles = api.get_candles("EURUSD_otc", 300, 1800)  # آخر 30 دقيقة
            print(f"✅ تم جلب {len(candles)} شمعة تاريخية")
            
            self.results['pocket_option_demo'] = True
            return True
            
        except Exception as e:
            error_msg = f"❌ فشل الاتصال بـ Pocket Option (تجريبي): {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_pocket_option_real(self):
        """اختبار الاتصال بـ Pocket Option - الحساب الحقيقي"""
        print("🟢 اختبار الاتصال بـ Pocket Option (الحساب الحقيقي)...")
        try:
            api = PocketOption(REAL_SSID)
            time.sleep(5)  # انتظار تأسيس الاتصال
            
            # اختبار جلب الرصيد
            balance = api.balance()
            print(f"✅ الرصيد الحقيقي: ${balance}")
            
            # اختبار جلب نسب الأرباح
            payouts = api.payout()
            print(f"✅ عدد الأصول المتاحة: {len(payouts)}")
            
            # اختبار جلب البيانات التاريخية لزوج واحد
            candles = api.get_candles("EURUSD_otc", 300, 1800)  # آخر 30 دقيقة
            print(f"✅ تم جلب {len(candles)} شمعة تاريخية")
            
            self.results['pocket_option_real'] = True
            return True
            
        except Exception as e:
            error_msg = f"❌ فشل الاتصال بـ Pocket Option (حقيقي): {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    async def test_async_connection(self):
        """اختبار الاتصال غير المتزامن"""
        print("🔄 اختبار الاتصال غير المتزامن...")
        try:
            api = PocketOptionAsync(DEMO_SSID)
            await asyncio.sleep(5)
            
            # اختبار جلب الرصيد بشكل غير متزامن
            balance = await api.balance()
            print(f"✅ الاتصال غير المتزامن نجح - الرصيد: ${balance}")
            
            return True
            
        except Exception as e:
            error_msg = f"❌ فشل الاتصال غير المتزامن: {str(e)}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار جميع الاتصالات...")
        print("=" * 60)
        
        # اختبار قواعد البيانات
        self.test_postgresql()
        print("-" * 40)
        self.test_redis()
        print("-" * 40)
        
        # اختبار منصة التداول
        self.test_pocket_option_demo()
        print("-" * 40)
        self.test_pocket_option_real()
        print("-" * 40)
        
        # اختبار الاتصال غير المتزامن
        asyncio.run(self.test_async_connection())
        
        # عرض النتائج النهائية
        self.show_results()
    
    def show_results(self):
        """عرض النتائج النهائية"""
        print("=" * 60)
        print("📊 ملخص نتائج الاختبارات:")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(self.results.values())
        
        for test_name, result in self.results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{test_name}: {status}")
        
        print("-" * 40)
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"الاختبارات الناجحة: {passed_tests}")
        print(f"نسبة النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print("\n🚨 الأخطاء المسجلة:")
            for error in self.errors:
                print(f"  - {error}")
        
        # تحديد حالة النجاح الإجمالية
        if passed_tests == total_tests:
            print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للمرحلة التالية.")
            return True
        else:
            print(f"\n⚠️ {total_tests - passed_tests} اختبار فشل. يجب إصلاح المشاكل قبل المتابعة.")
            return False

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام سكالبينغ احترافي - اختبار الاتصالات الأساسية")
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tester = ConnectionTester()
    success = tester.run_all_tests()
    
    # إنهاء البرنامج بحالة النجاح/الفشل
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
