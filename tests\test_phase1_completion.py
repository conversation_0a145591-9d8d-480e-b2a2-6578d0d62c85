#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار اكتمال المرحلة الأولى بنسبة 100%
اختبار صارم لجميع المتطلبات
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.database import db_config
from database.repositories import CurrencyPairRepository

class Phase1CompletionTest:
    """اختبار اكتمال المرحلة الأولى"""
    
    def __init__(self):
        self.results = {
            'config_70_pairs': False,
            'database_70_pairs': False,
            'postgresql_connection': False,
            'redis_connection': False,
            'file_structure': False
        }
        self.errors = []
    
    def test_config_70_pairs(self):
        """اختبار وجود 70 زوج في الإعدادات"""
        print("📊 اختبار عدد الأزواج في الإعدادات...")
        try:
            pairs = settings.CURRENCY_PAIRS
            count = len(pairs)
            
            if count == 70:
                print(f"✅ عدد الأزواج صحيح: {count}")
                self.results['config_70_pairs'] = True
                return True
            else:
                raise Exception(f"عدد الأزواج خطأ: {count} بدلاً من 70")
        except Exception as e:
            error_msg = f"❌ خطأ في عدد الأزواج: {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_database_70_pairs(self):
        """اختبار وجود 70 زوج في قاعدة البيانات"""
        print("💾 اختبار عدد الأزواج في قاعدة البيانات...")
        try:
            repo = CurrencyPairRepository()
            pairs = repo.get_all_pairs()
            count = len(pairs)
            
            if count == 70:
                print(f"✅ عدد الأزواج في قاعدة البيانات صحيح: {count}")
                self.results['database_70_pairs'] = True
                return True
            else:
                raise Exception(f"عدد الأزواج في قاعدة البيانات خطأ: {count} بدلاً من 70")
        except Exception as e:
            error_msg = f"❌ خطأ في قاعدة البيانات: {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_postgresql_connection(self):
        """اختبار اتصال PostgreSQL"""
        print("🔵 اختبار اتصال PostgreSQL...")
        try:
            conn = db_config.get_postgres_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM currency_pairs")
            result = cursor.fetchone()
            count = result['count'] if isinstance(result, dict) else result[0]
            cursor.close()
            conn.close()
            
            if count >= 70:
                print(f"✅ PostgreSQL متصل وبه {count} زوج")
                self.results['postgresql_connection'] = True
                return True
            else:
                raise Exception(f"عدد الأزواج في PostgreSQL: {count} (يجب أن يكون 70)")
        except Exception as e:
            error_msg = f"❌ خطأ في PostgreSQL: {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_redis_connection(self):
        """اختبار اتصال Redis"""
        print("🔴 اختبار اتصال Redis...")
        try:
            redis_client = db_config.get_redis_client()
            redis_client.ping()
            
            # اختبار كتابة وقراءة
            test_key = "phase1_test"
            redis_client.set(test_key, "success", ex=10)
            result = redis_client.get(test_key)
            redis_client.delete(test_key)
            
            if result == "success":
                print("✅ Redis متصل ويعمل بشكل صحيح")
                self.results['redis_connection'] = True
                return True
            else:
                raise Exception("فشل في اختبار الكتابة/القراءة")
        except Exception as e:
            error_msg = f"❌ خطأ في Redis: {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def test_file_structure(self):
        """اختبار هيكل الملفات"""
        print("📁 اختبار هيكل الملفات...")
        try:
            required_files = [
                '.env',
                'config/__init__.py',
                'config/settings.py',
                'config/database.py',
                'config/constants.py',
                'database/__init__.py',
                'database/models.py',
                'database/repositories.py',
                'database/setup_database.py',
                'data_collector/__init__.py',
                'data_collector/pocket_option_connector.py',
                'data_collector/live_data_collector.py',
                'data_collector/historical_data_collector.py'
            ]
            
            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            if not missing_files:
                print(f"✅ جميع الملفات المطلوبة موجودة ({len(required_files)} ملف)")
                self.results['file_structure'] = True
                return True
            else:
                raise Exception(f"ملفات مفقودة: {missing_files}")
        except Exception as e:
            error_msg = f"❌ خطأ في هيكل الملفات: {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار اكتمال المرحلة الأولى...")
        print("=" * 60)
        
        tests = [
            ("عدد الأزواج في الإعدادات", self.test_config_70_pairs),
            ("عدد الأزواج في قاعدة البيانات", self.test_database_70_pairs),
            ("اتصال PostgreSQL", self.test_postgresql_connection),
            ("اتصال Redis", self.test_redis_connection),
            ("هيكل الملفات", self.test_file_structure)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}...")
            test_func()
            print("-" * 40)
        
        # عرض النتائج النهائية
        self.show_results()
    
    def show_results(self):
        """عرض النتائج النهائية"""
        print("=" * 60)
        print("📊 ملخص نتائج اختبار اكتمال المرحلة الأولى:")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(self.results.values())
        
        for test_name, result in self.results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{test_name}: {status}")
        
        print("-" * 40)
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"الاختبارات الناجحة: {passed_tests}")
        print(f"نسبة النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print("\n🚨 الأخطاء المسجلة:")
            for error in self.errors:
                print(f"  - {error}")
        
        # تحديد حالة النجاح الإجمالية
        if passed_tests == total_tests:
            print("\n🎉 المرحلة الأولى مكتملة 100%!")
            print("✅ جاهز للانتقال للمرحلة الثانية")
            return True
        else:
            print(f"\n❌ المرحلة الأولى غير مكتملة")
            print(f"❌ يجب إصلاح {total_tests - passed_tests} مشكلة قبل المتابعة")
            return False

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام سكالبينغ احترافي - اختبار اكتمال المرحلة الأولى")
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tester = Phase1CompletionTest()
    success = tester.run_all_tests()
    
    # إنهاء البرنامج بحالة النجاح/الفشل
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
