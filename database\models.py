"""
نماذج قاعدة البيانات
جميع الجداول والعلاقات المطلوبة للنظام
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Index, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from config.database import Base

class CurrencyPair(Base):
    """جدول أزواج العملات"""
    __tablename__ = 'currency_pairs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_otc = Column(Boolean, default=False, nullable=False)
    category = Column(String(20), default='major', nullable=False)  # major, minor, exotic
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    candles = relationship("Candle", back_populates="currency_pair", cascade="all, delete-orphan")
    technical_indicators = relationship("TechnicalIndicator", back_populates="currency_pair", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="currency_pair", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<CurrencyPair(symbol='{self.symbol}', name='{self.name}')>"

class Candle(Base):
    """جدول الشموع"""
    __tablename__ = 'candles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    currency_pair_id = Column(Integer, ForeignKey('currency_pairs.id'), nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    timeframe = Column(Integer, default=300, nullable=False)  # 5 دقائق
    
    # بيانات الشمعة
    open_price = Column(Numeric(precision=10, scale=5), nullable=False)
    high_price = Column(Numeric(precision=10, scale=5), nullable=False)
    low_price = Column(Numeric(precision=10, scale=5), nullable=False)
    close_price = Column(Numeric(precision=10, scale=5), nullable=False)
    volume = Column(Numeric(precision=15, scale=2), default=0)
    
    # معلومات إضافية
    is_closed = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    currency_pair = relationship("CurrencyPair", back_populates="candles")
    technical_indicators = relationship("TechnicalIndicator", back_populates="candle", cascade="all, delete-orphan")
    
    # فهارس مركبة للأداء
    __table_args__ = (
        Index('idx_candle_pair_timestamp', 'currency_pair_id', 'timestamp'),
        Index('idx_candle_timestamp_timeframe', 'timestamp', 'timeframe'),
    )
    
    def __repr__(self):
        return f"<Candle(pair_id={self.currency_pair_id}, timestamp='{self.timestamp}', close={self.close_price})>"

class TechnicalIndicator(Base):
    """جدول المؤشرات الفنية"""
    __tablename__ = 'technical_indicators'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    currency_pair_id = Column(Integer, ForeignKey('currency_pairs.id'), nullable=False)
    candle_id = Column(Integer, ForeignKey('candles.id'), nullable=False)
    
    # معلومات المؤشر
    indicator_name = Column(String(50), nullable=False, index=True)
    indicator_value = Column(Numeric(precision=15, scale=8), nullable=True)
    indicator_signal = Column(String(10), nullable=True)  # BUY, SELL, NEUTRAL
    
    # قيم إضافية للمؤشرات المعقدة (مثل MACD, Bollinger Bands)
    additional_values = Column(Text, nullable=True)  # JSON format
    
    # معلومات الوقت
    timestamp = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # العلاقات
    currency_pair = relationship("CurrencyPair", back_populates="technical_indicators")
    candle = relationship("Candle", back_populates="technical_indicators")
    
    # فهارس مركبة للأداء
    __table_args__ = (
        Index('idx_indicator_pair_name_timestamp', 'currency_pair_id', 'indicator_name', 'timestamp'),
        Index('idx_indicator_candle_name', 'candle_id', 'indicator_name'),
    )
    
    def __repr__(self):
        return f"<TechnicalIndicator(name='{self.indicator_name}', value={self.indicator_value}, signal='{self.indicator_signal}')>"

class Trade(Base):
    """جدول الصفقات"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    currency_pair_id = Column(Integer, ForeignKey('currency_pairs.id'), nullable=False)
    
    # معلومات الصفقة
    trade_id = Column(String(50), unique=True, nullable=False, index=True)  # ID من المنصة
    account_type = Column(String(10), nullable=False)  # demo, real
    direction = Column(String(10), nullable=False)  # call, put
    amount = Column(Numeric(precision=10, scale=2), nullable=False)

    # أسعار ومعلومات التنفيذ
    entry_price = Column(Numeric(precision=10, scale=5), nullable=False)
    exit_price = Column(Numeric(precision=10, scale=5), nullable=True)
    payout_percentage = Column(Numeric(precision=5, scale=2), nullable=False)
    
    # أوقات الصفقة
    entry_time = Column(DateTime, nullable=False, index=True)
    expiry_time = Column(DateTime, nullable=False)
    exit_time = Column(DateTime, nullable=True)
    
    # نتائج الصفقة
    status = Column(String(20), default='pending', nullable=False)  # pending, win, loss, refund
    profit_loss = Column(Numeric(precision=10, scale=2), default=0)

    # معلومات إضافية
    strategy_used = Column(String(100), nullable=True)
    confidence_score = Column(Numeric(precision=5, scale=2), nullable=True)
    notes = Column(Text, nullable=True)
    
    # معلومات النظام
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # العلاقات
    currency_pair = relationship("CurrencyPair", back_populates="trades")
    
    # فهارس للأداء
    __table_args__ = (
        Index('idx_trade_pair_entry_time', 'currency_pair_id', 'entry_time'),
        Index('idx_trade_status_account', 'status', 'account_type'),
        Index('idx_trade_entry_expiry', 'entry_time', 'expiry_time'),
    )
    
    def __repr__(self):
        return f"<Trade(id='{self.trade_id}', direction='{self.direction}', amount={self.amount}, status='{self.status}')>"

class RiskSetting(Base):
    """جدول إعدادات إدارة المخاطر"""
    __tablename__ = 'risk_settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # إعدادات المخاطر
    setting_name = Column(String(100), unique=True, nullable=False, index=True)
    setting_value = Column(Numeric(precision=10, scale=4), nullable=False)
    setting_type = Column(String(20), nullable=False)  # percentage, amount, count
    description = Column(Text, nullable=True)
    
    # حالة الإعداد
    is_active = Column(Boolean, default=True, nullable=False)
    account_type = Column(String(10), default='both', nullable=False)  # demo, real, both
    
    # معلومات النظام
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<RiskSetting(name='{self.setting_name}', value={self.setting_value}, type='{self.setting_type}')>"

class SystemLog(Base):
    """جدول سجلات النظام"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # معلومات السجل
    level = Column(String(20), nullable=False, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    module = Column(String(100), nullable=False, index=True)
    message = Column(Text, nullable=False)
    
    # معلومات إضافية
    function_name = Column(String(100), nullable=True)
    line_number = Column(Integer, nullable=True)
    exception_info = Column(Text, nullable=True)
    
    # معلومات السياق
    currency_pair = Column(String(20), nullable=True)
    account_type = Column(String(10), nullable=True)
    trade_id = Column(String(50), nullable=True)
    
    # معلومات الوقت
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True)
    
    # فهارس للأداء
    __table_args__ = (
        Index('idx_log_level_timestamp', 'level', 'timestamp'),
        Index('idx_log_module_timestamp', 'module', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<SystemLog(level='{self.level}', module='{self.module}', timestamp='{self.timestamp}')>"
