# دليل شامل لمكتبة BinaryOptionsTools-v2

## 📋 جدول المحتويات
1. [نظرة عامة](#نظرة-عامة)
2. [التثبيت](#التثبيت)
3. [البدء السريع](#البدء-السريع)
4. [الوظائف الأساسية](#الوظائف-الأساسية)
5. [الوظائف المتقدمة](#الوظائف-المتقدمة)
6. [أمثلة شاملة](#أمثلة-شاملة)
7. [استكشاف الأخطاء](#استكشاف-الأخطاء)
8. [الأسئلة الشائعة](#الأسئلة-الشائعة)

---

## 🔍 نظرة عامة

**BinaryOptionsTools-v2** هي مكتبة Python قوية ومتقدمة للتداول في الخيارات الثنائية على منصة Pocket Option. تدعم المكتبة العمليات المتزامنة وغير المتزامنة وتوفر واجهة برمجية شاملة للتداول وتحليل البيانات.

### ✨ الميزات الرئيسية
- 🔄 دعم العمليات المتزامنة وغير المتزامنة
- 💹 تنفيذ الصفقات (شراء/بيع) مع إمكانية التحقق من النتائج
- 📊 جلب بيانات الشموع التاريخية على إطارات زمنية متعددة
- 💰 إدارة الحساب (الرصيد، الصفقات المفتوحة/المغلقة)
- 📈 البث المباشر للأسعار
- 🎯 الحصول على نسب الأرباح لجميع الأصول
- 🔧 إعدادات تكوين مرنة
- 📝 نظام سجلات متقدم

### 🎯 المنصات المدعومة
- **Pocket Option** (الحسابات الحقيقية والتجريبية)

### 💻 متطلبات النظام
- **نظام التشغيل**: Windows (حالياً)
- **Python**: 3.9 - 3.12
- **الاتصال**: إنترنت مستقر

---

## 📦 التثبيت

### التثبيت عبر pip
```bash
pip install binaryoptionstoolsv2==0.1.6a3
```

### التحقق من التثبيت
```python
import BinaryOptionsToolsV2
print("تم تثبيت المكتبة بنجاح!")
```

---

## 🚀 البدء السريع

### 1. الحصول على SSID
قبل استخدام المكتبة، تحتاج إلى الحصول على SSID من متصفحك:

1. افتح Pocket Option في المتصفح
2. اضغط F12 لفتح أدوات المطور
3. انتقل إلى تبويب Network
4. ابحث عن رسائل WebSocket
5. انسخ SSID بالتنسيق الكامل

**مثال على SSID صحيح:**
```
42["auth",{"session":"sblkni7rg1bo7pqipkqb9mcv66","isDemo":1,"uid":97111934,"platform":2,"isFastHistory":false}]
```

### 2. مثال بسيط
```python
from BinaryOptionsToolsV2.pocketoption import PocketOption
import time

# SSID الخاص بك
ssid = '42["auth",{"session":"sblkni7rg1bo7pqipkqb9mcv66","isDemo":1,"uid":99087051,"platform":2,"isFastHistory":true}]'

# إنشاء العميل
api = PocketOption(ssid)
time.sleep(5)  # انتظار تأسيس الاتصال

# جلب الرصيد
balance = api.balance()
print(f"الرصيد الحالي: ${balance}")

# جلب نسب الأرباح
payouts = api.payout()
print(f"عدد الأصول المتاحة: {len(payouts)}")
```

---

## 🔧 الوظائف الأساسية

### 💰 إدارة الحساب

#### جلب الرصيد
```python
balance = api.balance()
print(f"الرصيد: ${balance}")
```

#### الصفقات المفتوحة
```python
opened_deals = api.opened_deals()
print(f"الصفقات المفتوحة: {len(opened_deals)}")
for deal in opened_deals:
    print(f"ID: {deal['id']}, الأصل: {deal['asset']}, المبلغ: ${deal['amount']}")
```

#### الصفقات المغلقة
```python
closed_deals = api.closed_deals()
print(f"الصفقات المغلقة: {len(closed_deals)}")
for deal in closed_deals:
    print(f"الربح: ${deal['profit']}, النتيجة: {'ربح' if deal['profit'] > 0 else 'خسارة'}")
```

#### مسح الصفقات المغلقة من الذاكرة
```python
api.clear_closed_deals()
```

### 📊 نسب الأرباح

#### جلب جميع نسب الأرباح
```python
all_payouts = api.payout()
print(f"إجمالي الأصول: {len(all_payouts)}")

# ترتيب حسب نسبة الربح
sorted_payouts = dict(sorted(all_payouts.items(), key=lambda x: x[1], reverse=True))
print("أفضل 5 أصول:")
for i, (asset, payout) in enumerate(list(sorted_payouts.items())[:5]):
    print(f"{i+1}. {asset}: {payout}%")
```

#### نسبة ربح أصل محدد
```python
eurusd_payout = api.payout("EURUSD_otc")
print(f"نسبة ربح EUR/USD: {eurusd_payout}%")
```

#### نسب أرباح عدة أصول
```python
assets = ["EURUSD_otc", "GBPUSD_otc", "USDJPY_otc"]
payouts = api.payout(assets)
for asset, payout in zip(assets, payouts):
    print(f"{asset}: {payout}%")
```

### 💹 التداول

#### صفقة شراء (Call)
```python
# بدون انتظار النتيجة
trade_id, trade_data = api.buy("EURUSD_otc", 1.0, 60, check_win=False)
print(f"تم وضع صفقة شراء - ID: {trade_id}")

# مع انتظار النتيجة
trade_id, result = api.buy("EURUSD_otc", 1.0, 60, check_win=True)
print(f"نتيجة الصفقة: {result['result']}")
print(f"الربح: ${result['profit']}")
```

#### صفقة بيع (Put)
```python
# بدون انتظار النتيجة
trade_id, trade_data = api.sell("EURUSD_otc", 1.0, 60, check_win=False)
print(f"تم وضع صفقة بيع - ID: {trade_id}")

# مع انتظار النتيجة
trade_id, result = api.sell("EURUSD_otc", 1.0, 60, check_win=True)
print(f"نتيجة الصفقة: {result['result']}")
```

#### التحقق من نتيجة صفقة
```python
result = api.check_win(trade_id)
print(f"النتيجة: {result['result']}")  # "win", "loss", أو "draw"
print(f"الربح/الخسارة: ${result['profit']}")
```

### 📈 بيانات الشموع

#### جلب الشموع التاريخية
```python
# جلب شموع الساعة الماضية بإطار زمني دقيقة واحدة
candles = api.get_candles("EURUSD_otc", 60, 3600)
print(f"تم جلب {len(candles)} شمعة")

# عرض آخر شمعة
if candles:
    latest = candles[-1]
    print(f"آخر شمعة:")
    print(f"  الوقت: {latest['time']}")
    print(f"  الافتتاح: {latest['open']}")
    print(f"  الإغلاق: {latest['close']}")
    print(f"  الأعلى: {latest['high']}")
    print(f"  الأدنى: {latest['low']}")
```

#### الإطارات الزمنية المدعومة
- **60 ثانية** (1 دقيقة)
- **300 ثانية** (5 دقائق)
- **900 ثانية** (15 دقيقة)
- **1800 ثانية** (30 دقيقة)
- **3600 ثانية** (1 ساعة)

### 📡 البث المباشر

#### البث المباشر للأسعار
```python
# إنشاء بث مباشر
stream = api.subscribe_symbol("EURUSD_otc")

# استقبال البيانات المباشرة
count = 0
for candle in stream:
    print(f"شمعة مباشرة: {candle}")
    count += 1
    if count >= 10:  # إيقاف بعد 10 شموع
        break
```

#### البث المباشر المجمع
```python
# تجميع عدة شموع خام في شمعة واحدة
stream = api.subscribe_symbol_chuncked("EURUSD_otc", 5)
for candle in stream:
    print(f"شمعة مجمعة من 5 شموع: {candle}")
```

#### البث المباشر المؤقت
```python
from datetime import timedelta

# شموع كل 30 ثانية
stream = api.subscribe_symbol_timed("EURUSD_otc", timedelta(seconds=30))
for candle in stream:
    print(f"شمعة كل 30 ثانية: {candle}")
```

### 📜 البيانات التاريخية
```python
# جلب البيانات التاريخية لآخر 5 دقائق
history = api.history("EURUSD_otc", 300)
print(f"البيانات التاريخية: {len(history)} نقطة")
```

### ⏰ وقت الخادم
```python
server_time = api.get_server_time()
print(f"وقت الخادم: {server_time}")
```

---

## 🔧 الوظائف المتقدمة

### 🔄 العمليات غير المتزامنة

#### إعداد العميل غير المتزامن
```python
from BinaryOptionsToolsV2.pocketoption import PocketOptionAsync
import asyncio

async def main():
    ssid = 'your_ssid_here'
    api = PocketOptionAsync(ssid)
    await asyncio.sleep(5)  # انتظار تأسيس الاتصال

    # جلب الرصيد
    balance = await api.balance()
    print(f"الرصيد: ${balance}")

    # وضع صفقة
    trade_id, trade_data = await api.buy("EURUSD_otc", 1.0, 60)
    print(f"تم وضع الصفقة: {trade_id}")

# تشغيل الكود غير المتزامن
asyncio.run(main())
```

#### البث المباشر غير المتزامن
```python
async def stream_prices():
    api = PocketOptionAsync(ssid)
    await asyncio.sleep(5)

    stream = await api.subscribe_symbol("EURUSD_otc")
    async for candle in stream:
        print(f"شمعة مباشرة: {candle}")
        # يمكنك إضافة منطق التداول هنا
```

### ⚙️ إعدادات التكوين

#### إنشاء تكوين مخصص
```python
from BinaryOptionsToolsV2.config import Config

# إنشاء تكوين مخصص
config = Config()
config.timeout_secs = 60          # مهلة الاتصال
config.reconnect_time = 10        # وقت إعادة الاتصال
config.max_allowed_loops = 200    # الحد الأقصى للحلقات

# استخدام التكوين
api = PocketOption(ssid, config=config)
```

#### تكوين من قاموس
```python
config_dict = {
    "timeout_secs": 60,
    "reconnect_time": 10,
    "max_allowed_loops": 200,
    "urls": ["wss://backup1.com", "wss://backup2.com"]
}
api = PocketOption(ssid, config=config_dict)
```

#### تكوين من JSON
```python
config_json = '''
{
    "timeout_secs": 60,
    "reconnect_time": 10,
    "connection_initialization_timeout_secs": 30
}
'''
api = PocketOption(ssid, config=config_json)
```

### 📝 نظام السجلات

#### تفعيل السجلات
```python
from BinaryOptionsToolsV2.tracing import start_logs

# تفعيل السجلات
start_logs("logs/", "INFO", True)
```

#### إعدادات السجلات المتقدمة
```python
from BinaryOptionsToolsV2.tracing import LogBuilder

# إنشاء منشئ السجلات
log_builder = LogBuilder()

# إعداد ملف السجل
log_builder.log_file("trading.log", "DEBUG")

# إعداد عرض السجلات في الطرفية
log_builder.terminal("INFO")

# بناء إعدادات السجلات
log_builder.build()
```

#### استخدام Logger مخصص
```python
from BinaryOptionsToolsV2.tracing import Logger

logger = Logger()
logger.info("بدء التداول")
logger.debug("تفاصيل الاتصال")
logger.warn("تحذير: رصيد منخفض")
logger.error("خطأ في الاتصال")
```

### 🔍 المدققات (Validators)

#### إنشاء مدققات مخصصة
```python
from BinaryOptionsToolsV2.validator import Validator

# مدقق البداية
starts_validator = Validator.starts_with("42[")
print(starts_validator.check('42["auth"]'))  # True

# مدقق النهاية
ends_validator = Validator.ends_with("]")
print(ends_validator.check('42["auth"]'))  # True

# مدقق الاحتواء
contains_validator = Validator.contains("success")
print(contains_validator.check('{"status":"success"}'))  # True

# مدقق regex
regex_validator = Validator.regex(r'\d+')
print(regex_validator.check('123abc'))  # True

# مدقق مخصص
custom_validator = Validator.custom(lambda x: len(x) > 10)
print(custom_validator.check('hello world'))  # True
```

#### دمج المدققات
```python
# جميع الشروط يجب أن تتحقق
all_validator = Validator.all([
    Validator.starts_with("42"),
    Validator.contains("auth")
])

# أي شرط يكفي
any_validator = Validator.any([
    Validator.contains("success"),
    Validator.contains("completed")
])

# عكس النتيجة
not_validator = Validator.ne(Validator.contains("error"))
```

### 📨 الرسائل الخام

#### إرسال رسائل WebSocket خام
```python
# إرسال رسالة بدون انتظار رد
api.send_raw_message('42["ping"]')

# إرسال رسالة مع انتظار رد محدد
validator = Validator.starts_with('451-["signals/load"')
response = api.create_raw_order('42["signals/subscribe"]', validator)
print(f"الرد: {response}")
```

#### إرسال مع مهلة زمنية
```python
from datetime import timedelta

validator = Validator.contains('"status":"success"')
try:
    response = api.create_raw_order_with_timout(
        '42["trade/start"]',
        validator,
        timedelta(seconds=5)
    )
    print(f"نجح: {response}")
except TimeoutError:
    print("انتهت المهلة الزمنية")
```

#### إنشاء مكرر للرسائل
```python
validator = Validator.regex(r'{"price":\d+\.\d+}')
stream = api.create_raw_iterator(
    '42["price/subscribe"]',
    validator,
    timeout=timedelta(minutes=5)
)

for message in stream:
    price_data = json.loads(message)
    print(f"السعر الحالي: {price_data['price']}")
```

---

## 📚 أمثلة شاملة

### 🤖 بوت تداول بسيط
```python
from BinaryOptionsToolsV2.pocketoption import PocketOption
import time
import json

class SimpleTradingBot:
    def __init__(self, ssid):
        self.api = PocketOption(ssid)
        time.sleep(5)

    def analyze_and_trade(self, asset="EURUSD_otc"):
        # جلب البيانات التاريخية
        candles = self.api.get_candles(asset, 60, 300)  # آخر 5 دقائق

        if len(candles) < 3:
            return "بيانات غير كافية"

        # تحليل بسيط: اتجاه السعر
        last_3_closes = [c['close'] for c in candles[-3:]]

        if last_3_closes[-1] > last_3_closes[-2] > last_3_closes[-3]:
            # اتجاه صاعد - شراء
            trade_id, result = self.api.buy(asset, 1.0, 60, check_win=True)
            return f"صفقة شراء: {result['result']}, ربح: ${result['profit']}"

        elif last_3_closes[-1] < last_3_closes[-2] < last_3_closes[-3]:
            # اتجاه هابط - بيع
            trade_id, result = self.api.sell(asset, 1.0, 60, check_win=True)
            return f"صفقة بيع: {result['result']}, ربح: ${result['profit']}"

        return "لا يوجد إشارة واضحة"

# استخدام البوت
bot = SimpleTradingBot(ssid)
result = bot.analyze_and_trade()
print(result)
```

### 📊 تحليل شامل للأصول
```python
import json
from datetime import datetime

class AssetAnalyzer:
    def __init__(self, ssid):
        self.api = PocketOption(ssid)
        time.sleep(5)

    def analyze_all_assets(self, top_n=10):
        # جلب جميع الأصول
        all_payouts = self.api.payout()

        # ترتيب حسب نسبة الربح
        sorted_assets = dict(sorted(all_payouts.items(),
                                  key=lambda x: x[1], reverse=True))

        analysis_results = []

        for asset, payout in list(sorted_assets.items())[:top_n]:
            print(f"تحليل {asset}...")

            try:
                # جلب بيانات مختلفة الإطارات الزمنية
                candles_1m = self.api.get_candles(asset, 60, 3600)    # ساعة
                candles_5m = self.api.get_candles(asset, 300, 7200)   # ساعتين

                asset_analysis = {
                    "asset": asset,
                    "payout": payout,
                    "analysis_time": datetime.now().isoformat(),
                    "data_quality": {
                        "1m_candles": len(candles_1m),
                        "5m_candles": len(candles_5m)
                    }
                }

                # تحليل التقلبات
                if candles_1m:
                    prices = [c['close'] for c in candles_1m]
                    volatility = (max(prices) - min(prices)) / min(prices) * 100
                    asset_analysis["volatility_1h"] = round(volatility, 4)

                analysis_results.append(asset_analysis)

            except Exception as e:
                print(f"خطأ في تحليل {asset}: {e}")
                continue

        # حفظ النتائج
        with open("assets_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        return analysis_results

# استخدام المحلل
analyzer = AssetAnalyzer(ssid)
results = analyzer.analyze_all_assets(5)
print(f"تم تحليل {len(results)} أصل")
```

### 📈 مراقب الأسعار المباشر
```python
from BinaryOptionsToolsV2.pocketoption import PocketOptionAsync
import asyncio
import json

class PriceMonitor:
    def __init__(self, ssid):
        self.ssid = ssid
        self.alerts = {}

    async def add_price_alert(self, asset, target_price, direction="above"):
        """إضافة تنبيه سعر"""
        self.alerts[asset] = {
            "target": target_price,
            "direction": direction,
            "triggered": False
        }

    async def monitor_prices(self, assets):
        """مراقبة الأسعار المباشرة"""
        api = PocketOptionAsync(self.ssid)
        await asyncio.sleep(5)

        # إنشاء مهام مراقبة لكل أصل
        tasks = []
        for asset in assets:
            task = asyncio.create_task(self._monitor_asset(api, asset))
            tasks.append(task)

        # تشغيل جميع المهام
        await asyncio.gather(*tasks)

    async def _monitor_asset(self, api, asset):
        """مراقبة أصل واحد"""
        try:
            stream = await api.subscribe_symbol(asset)
            async for candle in stream:
                current_price = candle['close']

                # فحص التنبيهات
                if asset in self.alerts and not self.alerts[asset]['triggered']:
                    alert = self.alerts[asset]

                    if (alert['direction'] == "above" and current_price >= alert['target']) or \
                       (alert['direction'] == "below" and current_price <= alert['target']):

                        print(f"🚨 تنبيه: {asset} وصل إلى {current_price}")
                        self.alerts[asset]['triggered'] = True

                        # يمكنك إضافة منطق التداول التلقائي هنا
                        await self._auto_trade(api, asset, current_price)

                print(f"{asset}: {current_price}")

        except Exception as e:
            print(f"خطأ في مراقبة {asset}: {e}")

    async def _auto_trade(self, api, asset, price):
        """تداول تلقائي عند تفعيل التنبيه"""
        try:
            # مثال: وضع صفقة شراء
            trade_id, trade_data = await api.buy(asset, 1.0, 60)
            print(f"✅ تم وضع صفقة تلقائية: {trade_id}")
        except Exception as e:
            print(f"❌ خطأ في التداول التلقائي: {e}")

# استخدام مراقب الأسعار
async def main():
    monitor = PriceMonitor(ssid)

    # إضافة تنبيهات
    await monitor.add_price_alert("EURUSD_otc", 1.1350, "above")
    await monitor.add_price_alert("GBPUSD_otc", 1.2500, "below")

    # بدء المراقبة
    await monitor.monitor_prices(["EURUSD_otc", "GBPUSD_otc"])

# تشغيل المراقب
# asyncio.run(main())
```

### 💾 حفظ البيانات التلقائي
```python
import json
import os
from datetime import datetime, timedelta

class DataCollector:
    def __init__(self, ssid):
        self.api = PocketOption(ssid)
        time.sleep(5)
        self.data_dir = "market_data"
        os.makedirs(self.data_dir, exist_ok=True)

    def collect_weekly_data(self, assets, timeframes=[60, 300, 900]):
        """جمع بيانات أسبوعية لأصول متعددة"""
        week_seconds = 7 * 24 * 3600

        for asset in assets:
            print(f"جمع بيانات {asset}...")

            asset_data = {
                "asset": asset,
                "collection_time": datetime.now().isoformat(),
                "timeframes": {}
            }

            for timeframe in timeframes:
                try:
                    candles = self.api.get_candles(asset, timeframe, week_seconds)
                    asset_data["timeframes"][f"{timeframe}s"] = {
                        "candles": candles,
                        "count": len(candles),
                        "analysis": self._analyze_candles(candles)
                    }
                    print(f"  {timeframe}s: {len(candles)} شمعة")

                except Exception as e:
                    print(f"  خطأ في {timeframe}s: {e}")
                    asset_data["timeframes"][f"{timeframe}s"] = {"error": str(e)}

            # حفظ البيانات
            filename = f"{self.data_dir}/{asset}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(asset_data, f, ensure_ascii=False, indent=2)

            print(f"  تم حفظ البيانات في: {filename}")

    def _analyze_candles(self, candles):
        """تحليل بسيط للشموع"""
        if not candles:
            return {"error": "لا توجد بيانات"}

        prices = [c['close'] for c in candles]
        highs = [c['high'] for c in candles]
        lows = [c['low'] for c in candles]

        return {
            "count": len(candles),
            "price_range": {
                "min": min(lows),
                "max": max(highs),
                "current": prices[-1],
                "change": prices[-1] - prices[0],
                "change_percent": ((prices[-1] - prices[0]) / prices[0]) * 100
            },
            "volatility": (max(highs) - min(lows)) / min(lows) * 100,
            "trend": "صاعد" if prices[-1] > prices[0] else "هابط"
        }

# استخدام جامع البيانات
collector = DataCollector(ssid)
top_assets = ["EURUSD_otc", "GBPUSD_otc", "USDJPY_otc", "AUDCAD_otc", "CADJPY_otc"]
collector.collect_weekly_data(top_assets)
```

---

## 🔧 استكشاف الأخطاء

### ❌ الأخطاء الشائعة وحلولها

#### 1. خطأ في تحليل SSID
```
ValueError: PocketOptionError, Failed to parse SSID: Error parsing ssid string into object
```

**الحل:**
- تأكد من أن SSID بالتنسيق الكامل:
```python
# ✅ صحيح
ssid = '42["auth",{"session":"ds4obb81bv5aa6cihgt4dn8k6u","isDemo":1,"uid":97111934,"platform":2,"isFastHistory":false}]'

# ❌ خطأ
ssid = 'ds4obb81bv5aa6cihgt4dn8k6u'
```

#### 2. خطأ في الاتصال
```
ConnectionError: Failed to connect to WebSocket
```

**الحل:**
- تحقق من الاتصال بالإنترنت
- تأكد من صحة SSID
- جرب إعادة تسجيل الدخول في المتصفح

#### 3. انتهاء مهلة الاتصال
```
TimeoutError: Operation timed out
```

**الحل:**
```python
# زيادة مهلة الاتصال
config = Config()
config.timeout_secs = 120  # دقيقتان
config.connection_initialization_timeout_secs = 60

api = PocketOption(ssid, config=config)
```

#### 4. خطأ في الوظائف غير المتوفرة
```
AttributeError: 'PocketOption' object has no attribute 'function_name'
```

**الحل:**
- تحقق من إصدار المكتبة
- راجع الوثائق للوظائف المتاحة
- استخدم الوظائف البديلة

### 🔍 تشخيص المشاكل

#### تفعيل السجلات التفصيلية
```python
from BinaryOptionsToolsV2.tracing import start_logs

# تفعيل السجلات التفصيلية
start_logs("debug_logs/", "DEBUG", True)
```

#### فحص حالة الاتصال
```python
try:
    api = PocketOption(ssid)
    time.sleep(5)

    # اختبار الاتصال
    balance = api.balance()
    print(f"الاتصال ناجح - الرصيد: ${balance}")

except Exception as e:
    print(f"فشل الاتصال: {e}")
    # إضافة تفاصيل إضافية للتشخيص
    import traceback
    traceback.print_exc()
```

#### اختبار الوظائف تدريجياً
```python
def test_functions_step_by_step(ssid):
    """اختبار الوظائف خطوة بخطوة"""
    try:
        print("1. إنشاء العميل...")
        api = PocketOption(ssid)
        time.sleep(5)
        print("✅ تم إنشاء العميل")

        print("2. اختبار الرصيد...")
        balance = api.balance()
        print(f"✅ الرصيد: ${balance}")

        print("3. اختبار نسب الأرباح...")
        payouts = api.payout()
        print(f"✅ الأصول المتاحة: {len(payouts)}")

        print("4. اختبار الشموع...")
        candles = api.get_candles("EURUSD_otc", 60, 300)
        print(f"✅ الشموع: {len(candles)}")

        print("🎉 جميع الاختبارات نجحت!")

    except Exception as e:
        print(f"❌ فشل في الخطوة: {e}")
        return False

    return True

# تشغيل الاختبار
test_functions_step_by_step(ssid)
```

---

## ❓ الأسئلة الشائعة

### 1. كيف أحصل على SSID؟
افتح Pocket Option في المتصفح، اضغط F12، انتقل إلى Network، ابحث عن WebSocket، وانسخ SSID الكامل.

### 2. هل يمكنني استخدام المكتبة مع حساب حقيقي؟
نعم، المكتبة تدعم الحسابات الحقيقية والتجريبية. تأكد من اختبار الكود على حساب تجريبي أولاً.

### 3. ما هي الإطارات الزمنية المدعومة؟
60s (1m), 300s (5m), 900s (15m), 1800s (30m), 3600s (1h)

### 4. كيف أتعامل مع انقطاع الاتصال؟
المكتبة تحتوي على آلية إعادة الاتصال التلقائي. يمكنك تخصيص إعدادات إعادة الاتصال في التكوين.

### 5. هل يمكنني تشغيل عدة عملاء في نفس الوقت؟
نعم، يمكنك إنشاء عدة عملاء، لكن تأكد من استخدام SSID مختلف لكل حساب.

### 6. كيف أحفظ البيانات للتحليل اللاحق؟
استخدم مكتبة json أو pandas لحفظ البيانات في ملفات JSON أو CSV.

### 7. هل المكتبة آمنة للاستخدام؟
المكتبة تستخدم اتصالات WebSocket آمنة، لكن تأكد من حماية SSID الخاص بك.

### 8. كيف أتعامل مع الأخطاء؟
استخدم try-except blocks وفعّل السجلات للحصول على تفاصيل الأخطاء.

---

## 📞 الدعم والمساعدة

- **Discord**: [انضم إلى الخادم](https://discord.gg/p7YyFqSmAz)
- **GitHub**: [مستودع المشروع](https://github.com/ChipaDevTeam/BinaryOptionsTools-v2)
- **الوثائق**: راجع ملفات README في المستودع

---

## ⚠️ تنبيهات مهمة

1. **المخاطر**: التداول في الخيارات الثنائية ينطوي على مخاطر عالية
2. **الاختبار**: اختبر دائماً على حساب تجريبي أولاً
3. **الأمان**: لا تشارك SSID الخاص بك مع أحد
4. **القوانين**: تأكد من قانونية التداول في بلدك
5. **المسؤولية**: استخدم المكتبة على مسؤوليتك الخاصة

---

## 📝 خاتمة

هذا الدليل يغطي جميع جوانب استخدام مكتبة BinaryOptionsTools-v2. للحصول على أحدث المعلومات والتحديثات، راجع المستودع الرسمي على GitHub.

**تاريخ آخر تحديث**: 25 مايو 2025
**إصدار المكتبة**: 0.1.6a3

---

## 🧪 نتائج اختبار الحد الأقصى للشموع التاريخية

### ✅ **اكتشاف مهم: المنصة تحدد عدد الشموع وليس الفترة الزمنية!**

#### 📊 **النتائج الأساسية - الزوج YERUSD_otc:**

**الحد الأقصى للشموع: ~149 شمعة** (ثابت لجميع الإطارات الزمنية)

| الإطار الزمني | عدد الشموع | التغطية الزمنية الفعلية | التغطية % |
|---------------|-------------|------------------------|------------|
| 1 دقيقة (60s) | 141-147 شمعة | 2.4 ساعة | 93-96% |
| 5 دقائق (300s) | 149 شمعة | 12.4 ساعة | 98-99% |
| 15 دقيقة (900s) | 149 شمعة | 37.2 ساعة (1.6 يوم) | 5.2% |
| 30 دقيقة (1800s) | 149 شمعة | 74.5 ساعة (3.1 يوم) | 10.3% |
| 1 ساعة (3600s) | 150 شمعة | 150 ساعة (6.2 أيام) | 20.8% |
| 4 ساعات (14400s) | 150 شمعة | 600 ساعة (25 يوم) | 83.3% |
| **24 ساعة (86400s)** | **30 شمعة** | **720 ساعة (30 يوم)** | **100%** |

#### 🏦 **نتائج الحسابات:**

**الحساب التجريبي:**
- ✅ جميع الإطارات الزمنية تعمل
- 📊 أقصى شموع: 149 (إطار زمني 5 دقائق)
- 🎯 معدل النجاح: 100%

**الحساب الحقيقي:**
- ✅ جميع الإطارات الزمنية تعمل
- 📊 أقصى شموع: 149 (إطار زمني 5 دقائق)
- 🎯 معدل النجاح: 100%
- 🔧 **SSID المطلوب**: `"isFastHistory": false`

#### 🎯 **القرار النهائي للنظام:**

**🔒 إطار زمني ثابت: 5 دقائق (300 ثانية)**

**مبررات الاختيار:**
1. **عدد شموع ممتاز**: 149 شمعة
2. **تغطية زمنية مناسبة**: 12.4 ساعة من البيانات
3. **تغطية عالية للفترات القصيرة**: 98-99%
4. **مناسب للتداول السريع**: تحديث كل 5 دقائق
5. **توازن مثالي**: بين الدقة والتغطية الزمنية
6. **استقرار البيانات**: أقل ضوضاء من الدقيقة الواحدة

#### 🔧 **الطرق الناجحة:**

```python
# للحساب التجريبي
history_data = await api.history(asset, period_seconds)

# للحساب الحقيقي
candles = await api.get_candles(asset, timeframe_seconds, period_seconds)
```

#### ⚠️ **مواصفات النظام النهائية:**

**🔧 الإعدادات المؤكدة:**

#### ✅ **تم إكمال المرحلة الثانية بنجاح - 2025-06-23**

**📊 نتائج طبقة جمع البيانات:**
- ✅ **الاتصال بالمنصة**: نجح (183 أصل متاح)
- ✅ **جمع البيانات التاريخية**: 148 شمعة لكل زوج من 5 أزواج
- ✅ **البث المباشر**: يعمل مع polling كل 30 ثانية
- ✅ **تخزين البيانات**: Redis + PostgreSQL يعمل بشكل مثالي
- ✅ **النظام متكامل**: يعمل 24/7 مع إعادة اتصال تلقائي
- ✅ **الأداء**: ممتاز (أقل من ثانية واحدة)

**🎯 الخطوة التالية:** المرحلة 3 - تطوير محرك المؤشرات الفنية
- **الإطار الزمني**: 5 دقائق (300 ثانية) - ثابت
- **عدد الشموع**: 149 شمعة
- **التغطية الزمنية**: 12.4 ساعة من البيانات التاريخية
- **معدل التحديث**: كل 5 دقائق
- **الحسابات المدعومة**: تجريبي + حقيقي
- **الطريقة للتجريبي**: `api.history(asset, period)`
- **الطريقة للحقيقي**: `api.get_candles(asset, 300, period)`

**✅ مزايا إطار زمني 5 دقائق:**
- استقرار البيانات (أقل ضوضاء)
- تغطية زمنية كافية للتحليل
- سرعة تحديث مناسبة للتداول
- توافق ممتاز مع كلا الحسابين

---

## 🎯 **القرار النهائي المؤكد**

### ✅ **إطار زمني النظام: 5 دقائق**

**تم اختبار جميع الإطارات الزمنية وتأكيد أن إطار زمني 5 دقائق هو الأمثل للنظام.**

**المواصفات النهائية:**
- 🕐 **الإطار الزمني**: 5 دقائق (300 ثانية)
- 📊 **عدد الشموع**: 149 شمعة
- ⏰ **التغطية**: 12.4 ساعة من البيانات
- 🔄 **التحديث**: كل 5 دقائق
- 🎯 **الاستخدام**: جميع مكونات النظام

**جاهز للبدء في بناء النظام!** 🚀
