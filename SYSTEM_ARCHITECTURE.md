# 🏗️ المعمارية التقنية الشاملة - نظام تداول سكالبينغ

## 📊 قرار المكدس التقني

### 🔧 التقنيات الأساسية
- **Python 3.9+** (البيئة: scalping_env)
- **PostgreSQL** (التخزين الدائم)
- **Redis** (التخزين المؤقت والبث المباشر)
- **BinaryOptionsTools-v2** (الاتصال بـ Pocket Option)
- **Streamlit** (واجهة الويب)
- **asyncio** (العمليات غير المتزامنة)

### 📚 مكتبات التحليل والذكاء الاصطناعي
- **Pandas, NumPy** (معالجة البيانات)
- **TA-Lib, pandas-ta** (المؤشرات الفنية)
- **Scikit-learn** (التعلم الآلي)
- **XGBoost** (نماذج التصنيف)
- **TensorFlow/Keras** (LSTM للتنبؤ)
- **SQLAlchemy** (ORM لقاعدة البيانات)

## 🔄 مخطط التدفق المعماري

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Pocket Option │────│  Data Collector │────│     Redis       │
│    WebSocket    │    │   (Layer 1)     │    │  (Live Data)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │◄───│ Technical Indic.│◄───│ Behavioral Anal.│
│ (Historical)    │    │   (Layer 2)     │    │   (Layer 3)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │◄───│  AI Predictor   │◄───│ Execution Engine│
│   Web UI        │    │   (Layer 4)     │    │   (Layer 5)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🗂️ هيكل المشروع المفصل

```
scalping_trading_system/
├── main.py                     # نقطة الدخول الرئيسية
├── config/
│   ├── __init__.py
│   ├── settings.py             # إعدادات النظام
│   ├── database.py             # إعدادات قواعد البيانات
│   └── constants.py            # الثوابت
├── data_collector/
│   ├── __init__.py
│   ├── pocket_option_client.py # عميل الاتصال
│   ├── data_manager.py         # إدارة البيانات
│   ├── websocket_handler.py    # معالج WebSocket
│   └── data_validator.py       # التحقق من البيانات
├── technical_analysis/
│   ├── __init__.py
│   ├── indicators_engine.py    # محرك المؤشرات
│   ├── pattern_analyzer.py     # تحليل الأنماط
│   ├── signal_filter.py        # فلترة الإشارات
│   └── trend_detector.py       # كشف الاتجاهات
├── behavioral_analysis/
│   ├── __init__.py
│   ├── candle_behavior.py      # سلوك الشموع
│   ├── quantitative_calc.py    # الحسابات الكمية
│   ├── time_filters.py         # فلاتر الوقت
│   └── correlation_analyzer.py # تحليل الارتباطات
├── ai_predictor/
│   ├── __init__.py
│   ├── model_manager.py        # إدارة النماذج
│   ├── data_preprocessor.py    # معالجة البيانات
│   ├── training_engine.py      # محرك التدريب
│   ├── prediction_service.py   # خدمة التنبؤ
│   └── models/                 # ملفات النماذج المحفوظة
├── execution_engine/
│   ├── __init__.py
│   ├── decision_maker.py       # صانع القرار
│   ├── trade_executor.py       # منفذ الصفقات
│   ├── risk_manager.py         # إدارة المخاطر
│   └── trade_monitor.py        # مراقب الصفقات
├── database/
│   ├── __init__.py
│   ├── models.py               # نماذج قاعدة البيانات
│   ├── repositories.py         # مستودعات البيانات
│   └── migrations/             # ترحيلات قاعدة البيانات
├── web_interface/
│   ├── __init__.py
│   ├── app.py                  # تطبيق Streamlit
│   ├── components/             # مكونات الواجهة
│   ├── pages/                  # صفحات الواجهة
│   └── utils/                  # أدوات مساعدة
├── utils/
│   ├── __init__.py
│   ├── logger.py               # نظام السجلات
│   ├── helpers.py              # دوال مساعدة
│   └── validators.py           # مدققات
├── tests/
│   ├── __init__.py
│   ├── test_data_collector.py
│   ├── test_technical_analysis.py
│   ├── test_ai_predictor.py
│   └── integration_tests.py
├── requirements.txt            # المتطلبات
├── .env                        # متغيرات البيئة
├── docker-compose.yml          # إعداد Docker
└── README.md                   # دليل المشروع
```

## 🗄️ تصميم قاعدة البيانات

### PostgreSQL Tables

```sql
-- جدول أزواج العملات
CREATE TABLE currency_pairs (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الشموع التاريخية
CREATE TABLE candles (
    id BIGSERIAL PRIMARY KEY,
    pair_id INTEGER REFERENCES currency_pairs(id),
    timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(10,5) NOT NULL,
    high_price DECIMAL(10,5) NOT NULL,
    low_price DECIMAL(10,5) NOT NULL,
    close_price DECIMAL(10,5) NOT NULL,
    volume DECIMAL(15,2) DEFAULT 0,
    timeframe INTEGER DEFAULT 300,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(pair_id, timestamp, timeframe)
);

-- جدول المؤشرات الفنية
CREATE TABLE technical_indicators (
    id BIGSERIAL PRIMARY KEY,
    candle_id BIGINT REFERENCES candles(id),
    indicator_name VARCHAR(50) NOT NULL,
    value DECIMAL(15,8),
    metadata JSONB,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الصفقات
CREATE TABLE trades (
    id BIGSERIAL PRIMARY KEY,
    pair_id INTEGER REFERENCES currency_pairs(id),
    trade_type VARCHAR(10) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    entry_price DECIMAL(10,5) NOT NULL,
    entry_time TIMESTAMP NOT NULL,
    expiry_time TIMESTAMP NOT NULL,
    exit_price DECIMAL(10,5),
    result VARCHAR(10),
    profit_loss DECIMAL(10,2),
    confidence_score DECIMAL(5,2),
    technical_signals JSONB,
    behavioral_signals JSONB,
    ai_prediction JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات إدارة المخاطر
CREATE TABLE risk_settings (
    id SERIAL PRIMARY KEY,
    setting_name VARCHAR(50) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Redis Structure
```
live_data:{pair_symbol} -> Hash {
    current_price: float,
    timestamp: timestamp,
    volume: float,
    bid: float,
    ask: float
}

indicators:{pair_symbol}:{timestamp} -> Hash {
    ema5: float,
    ema10: float,
    rsi: float,
    macd: float,
    ...
}

signals:{pair_symbol} -> List [
    {signal_data, timestamp, confidence}
]
```

## 🔌 APIs وواجهات النظام

### 1. Data Collector API
```python
class DataCollectorAPI:
    async def connect_to_platform()
    async def collect_historical_data(pairs: List[str])
    async def start_live_streaming(pairs: List[str])
    async def validate_data_completeness()
    async def store_candle_data()
```

### 2. Technical Analysis API
```python
class TechnicalAnalysisAPI:
    def calculate_indicators(candle_data: DataFrame)
    def analyze_patterns(candles: List[Candle])
    def filter_signals(signals: List[Signal])
    def detect_trends(price_data: List[float])
```

### 3. AI Predictor API
```python
class AIPredictorAPI:
    async def train_models(training_data: DataFrame)
    async def predict_direction(features: Dict)
    def calculate_confidence(prediction: Dict)
    def classify_market_state(market_data: Dict)
```

### 4. Execution Engine API
```python
class ExecutionEngineAPI:
    async def make_trading_decision(signals: Dict)
    async def execute_trade(trade_params: Dict)
    def apply_risk_management(trade: Trade)
    async def monitor_trade_results()
```

## ⚡ نظام المعالجة غير المتزامنة

```python
async def main_trading_loop():
    tasks = [
        asyncio.create_task(data_collector.stream_live_data()),
        asyncio.create_task(technical_analyzer.process_indicators()),
        asyncio.create_task(behavioral_analyzer.analyze_patterns()),
        asyncio.create_task(ai_predictor.generate_predictions()),
        asyncio.create_task(execution_engine.monitor_opportunities())
    ]
    
    await asyncio.gather(*tasks)
```

## 📊 نظام المراقبة والتقارير

### مؤشرات الأداء
- معدل نجاح الصفقات
- متوسط الربح/الخسارة  
- وقت الاستجابة
- استقرار الاتصال

### التنبيهات
- انقطاع الاتصال
- أخطاء في البيانات
- تجاوز حدود المخاطر

## 🎯 بيانات الاتصال

### SSID الحساب التجريبي
```
42["auth",{"session":"sblkni7rg1bo7pqipkqb9mcv66","isDemo":1,"uid":99087051,"platform":2,"isFastHistory":true}]
```

### SSID الحساب الحقيقي  
```
42["auth",{"session":"a:4:{s:10:\"session_id\";s:32:\"866de092f3f3be07766382237cd7882a\";s:10:\"ip_address\";s:14:\"**************\";s:10:\"user_agent\";s:111:\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\";s:13:\"last_activity\";i:1750644298;}03f4c73c659cf2c5979b629b118ffe92","isDemo":0,"uid":99087051,"platform":2,"isFastHistory":true}]
```
