#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات
إنشاء قاعدة البيانات والجداول والبيانات الأولية
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine

from config.settings import settings
from config.database import Base, db_config
from database.models import CurrencyPair, RiskSetting
from config.constants import ALL_CURRENCY_PAIRS, DEFAULT_RISK_SETTINGS

class DatabaseSetup:
    """فئة إعداد قاعدة البيانات"""
    
    def __init__(self):
        self.admin_connection = None
        self.engine = None
    
    def create_database(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        print("🔵 إنشاء قاعدة البيانات...")
        
        try:
            # الاتصال بـ PostgreSQL كمدير
            self.admin_connection = psycopg2.connect(
                host=settings.DB_HOST,
                port=settings.DB_PORT,
                user=settings.DB_USER,
                password=settings.DB_PASSWORD,
                database='postgres'  # قاعدة البيانات الافتراضية
            )
            self.admin_connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            cursor = self.admin_connection.cursor()
            
            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (settings.DB_NAME,)
            )
            
            if cursor.fetchone():
                print(f"✅ قاعدة البيانات '{settings.DB_NAME}' موجودة بالفعل")
            else:
                # إنشاء قاعدة البيانات
                cursor.execute(f'CREATE DATABASE "{settings.DB_NAME}"')
                print(f"✅ تم إنشاء قاعدة البيانات '{settings.DB_NAME}' بنجاح")
            
            cursor.close()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
        finally:
            if self.admin_connection:
                self.admin_connection.close()
    
    def create_tables(self):
        """إنشاء جميع الجداول"""
        print("📊 إنشاء الجداول...")
        
        try:
            # إنشاء محرك الاتصال
            self.engine = create_engine(settings.DATABASE_URL)
            
            # إنشاء جميع الجداول
            Base.metadata.create_all(self.engine)
            print("✅ تم إنشاء جميع الجداول بنجاح")
            
            # عرض الجداول المُنشأة
            inspector = self.engine.dialect.get_table_names(self.engine.connect())
            print(f"📋 الجداول المُنشأة: {', '.join(inspector)}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
            return False
    
    def insert_currency_pairs(self):
        """إدراج أزواج العملات الـ70"""
        print("💱 إدراج أزواج العملات...")
        
        try:
            session = db_config.get_postgres_session()
            
            # التحقق من وجود البيانات
            existing_count = session.query(CurrencyPair).count()
            if existing_count > 0:
                print(f"✅ يوجد {existing_count} زوج عملة في قاعدة البيانات")
                return True
            
            # إدراج أزواج العملات
            pairs_added = 0
            for symbol in ALL_CURRENCY_PAIRS:
                # تحديد الفئة
                if symbol.endswith('_otc'):
                    category = 'otc'
                    is_otc = True
                    name = symbol.replace('_otc', '').upper()
                else:
                    category = 'major' if symbol in ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'] else 'minor'
                    is_otc = False
                    name = symbol.upper()
                
                # إنشاء زوج العملة
                currency_pair = CurrencyPair(
                    symbol=symbol,
                    name=name,
                    is_active=True,
                    is_otc=is_otc,
                    category=category
                )
                
                session.add(currency_pair)
                pairs_added += 1
            
            session.commit()
            print(f"✅ تم إدراج {pairs_added} زوج عملة بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إدراج أزواج العملات: {e}")
            session.rollback()
            return False
        finally:
            session.close()
    
    def insert_risk_settings(self):
        """إدراج إعدادات إدارة المخاطر الافتراضية"""
        print("🛡️ إدراج إعدادات إدارة المخاطر...")
        
        try:
            session = db_config.get_postgres_session()
            
            # التحقق من وجود البيانات
            existing_count = session.query(RiskSetting).count()
            if existing_count > 0:
                print(f"✅ يوجد {existing_count} إعداد مخاطر في قاعدة البيانات")
                return True
            
            # إدراج إعدادات المخاطر
            settings_added = 0
            for setting_name, setting_value in DEFAULT_RISK_SETTINGS.items():
                # تحديد نوع الإعداد
                if 'percent' in setting_name:
                    setting_type = 'percentage'
                elif 'amount' in setting_name:
                    setting_type = 'amount'
                else:
                    setting_type = 'count'
                
                # وصف الإعداد
                descriptions = {
                    'max_daily_loss_percent': 'الحد الأقصى للخسارة اليومية كنسبة مئوية',
                    'max_concurrent_trades': 'الحد الأقصى للصفقات المتزامنة',
                    'min_balance_threshold': 'الحد الأدنى للرصيد المطلوب',
                    'risk_per_trade_percent': 'نسبة المخاطرة لكل صفقة',
                    'max_trade_amount': 'الحد الأقصى لمبلغ الصفقة',
                    'min_trade_amount': 'الحد الأدنى لمبلغ الصفقة'
                }
                
                risk_setting = RiskSetting(
                    setting_name=setting_name,
                    setting_value=setting_value,
                    setting_type=setting_type,
                    description=descriptions.get(setting_name, ''),
                    is_active=True,
                    account_type='both'
                )
                
                session.add(risk_setting)
                settings_added += 1
            
            session.commit()
            print(f"✅ تم إدراج {settings_added} إعداد مخاطر بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إدراج إعدادات المخاطر: {e}")
            session.rollback()
            return False
        finally:
            session.close()
    
    def test_database_operations(self):
        """اختبار العمليات الأساسية على قاعدة البيانات"""
        print("🧪 اختبار العمليات الأساسية...")
        
        try:
            session = db_config.get_postgres_session()
            
            # اختبار قراءة أزواج العملات
            pairs_count = session.query(CurrencyPair).count()
            print(f"✅ عدد أزواج العملات: {pairs_count}")
            
            # اختبار قراءة إعدادات المخاطر
            risk_count = session.query(RiskSetting).count()
            print(f"✅ عدد إعدادات المخاطر: {risk_count}")
            
            # اختبار استعلام معقد
            active_pairs = session.query(CurrencyPair).filter(CurrencyPair.is_active == True).count()
            print(f"✅ عدد الأزواج النشطة: {active_pairs}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار العمليات: {e}")
            return False
        finally:
            session.close()
    
    def setup_complete_database(self):
        """إعداد قاعدة البيانات بالكامل"""
        print("🚀 بدء إعداد قاعدة البيانات الكامل...")
        print("=" * 60)
        
        steps = [
            ("إنشاء قاعدة البيانات", self.create_database),
            ("إنشاء الجداول", self.create_tables),
            ("إدراج أزواج العملات", self.insert_currency_pairs),
            ("إدراج إعدادات المخاطر", self.insert_risk_settings),
            ("اختبار العمليات", self.test_database_operations)
        ]
        
        success_count = 0
        for step_name, step_function in steps:
            print(f"\n📋 {step_name}...")
            if step_function():
                success_count += 1
                print(f"✅ {step_name} - مكتمل")
            else:
                print(f"❌ {step_name} - فشل")
                break
            print("-" * 40)
        
        # النتيجة النهائية
        print("\n" + "=" * 60)
        print("📊 ملخص إعداد قاعدة البيانات:")
        print(f"الخطوات المكتملة: {success_count}/{len(steps)}")
        
        if success_count == len(steps):
            print("🎉 تم إعداد قاعدة البيانات بنجاح!")
            print("✅ النظام جاهز للمرحلة التالية")
            return True
        else:
            print("⚠️ فشل في إعداد قاعدة البيانات")
            print("❌ يجب إصلاح المشاكل قبل المتابعة")
            return False

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام سكالبينغ احترافي - إعداد قاعدة البيانات")
    print(f"⏰ وقت الإعداد: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # إنشاء مثيل إعداد قاعدة البيانات
    db_setup = DatabaseSetup()
    
    # تشغيل الإعداد الكامل
    success = db_setup.setup_complete_database()
    
    # إنهاء البرنامج
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
