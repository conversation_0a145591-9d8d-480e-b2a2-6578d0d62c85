"""
جامع البيانات التاريخية
جمع البيانات التاريخية من منصة Pocket Option
"""

import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime

from .pocket_option_connector import PocketOptionConnector

logger = logging.getLogger(__name__)

class HistoricalDataCollector:
    """جامع البيانات التاريخية"""
    
    def __init__(self, account_type: str = 'demo'):
        self.account_type = account_type
        self.connector = PocketOptionConnector(account_type)
        
        logger.info(f"تم إنشاء جامع البيانات التاريخية - نوع الحساب: {account_type}")
    
    async def collect_historical_data(self, symbol: str, timeframe: int, count: int) -> List[Dict]:
        """جمع البيانات التاريخية لزوج معين"""
        logger.info(f"جمع البيانات التاريخية لـ {symbol}...")
        
        if await self.connector.connect():
            try:
                candles = await self.connector.get_candles(symbol, timeframe, count)
                logger.info(f"✅ تم جمع {len(candles)} شمعة لـ {symbol}")
                return candles
            except Exception as e:
                logger.error(f"❌ خطأ في جمع البيانات لـ {symbol}: {e}")
                return []
            finally:
                await self.connector.disconnect()
        else:
            logger.error(f"❌ فشل في الاتصال لجمع بيانات {symbol}")
            return []
    
    async def collect_multiple_pairs(self, pairs: List[str], timeframe: int, count: int) -> Dict[str, List[Dict]]:
        """جمع البيانات التاريخية لعدة أزواج"""
        logger.info(f"جمع البيانات التاريخية لـ {len(pairs)} زوج...")
        
        results = {}
        
        if await self.connector.connect():
            try:
                for symbol in pairs:
                    try:
                        candles = await self.connector.get_candles(symbol, timeframe, count)
                        results[symbol] = candles
                        logger.debug(f"✅ {symbol}: {len(candles)} شمعة")
                        
                        # انتظار قصير بين الطلبات
                        await asyncio.sleep(0.5)
                        
                    except Exception as e:
                        logger.error(f"❌ خطأ في جمع بيانات {symbol}: {e}")
                        results[symbol] = []
                
                logger.info(f"✅ تم جمع البيانات لـ {len(results)} زوج")
                
            finally:
                await self.connector.disconnect()
        else:
            logger.error("❌ فشل في الاتصال لجمع البيانات")
        
        return results
