(self.webpackChunkcatboost_widget=self.webpackChunkcatboost_widget||[]).push([[138],{214:(t,e,a)=>{"use strict";a.d(e,{Z:()=>o});var r=a(645),s=a.n(r)()((function(t){return t[1]}));s.push([t.id,".highcharts-tooltip {\r\n    display: none !important;\r\n}\r\n.highcharts-halo {\r\n    display: none !important;\r\n}\r\n\r\n.catboost {\r\n    position: relative;\r\n}\r\n\r\n.catboost-panel {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 280px;\r\n}\r\n\r\n.catboost-panel__controls {\r\n    margin-left: 0;\r\n}\r\n\r\n.catboost-panel__controls_label {\r\n    padding: 5px 0 0 8px;\r\n    cursor: pointer;\r\n    width: 80px;\r\n    box-sizing: content-box;\r\n}\r\n.catboost-panel__controls_label_time {\r\n    width: inherit;\r\n}\r\n\r\n.catboost-panel__controls2 {\r\n    margin-top: 10px;\r\n}\r\n\r\n.catboost-panel__controls2_label {\r\n    padding: 5px 11px 0 8px;\r\n    cursor: pointer;\r\n    width: 90px;\r\n    box-sizing: content-box;\r\n}\r\n.catboost-panel__controls2_label-long {\r\n    width: 170px;\r\n}\r\n\r\n.catboost-panel__series {\r\n    height: 340px;\r\n    overflow-y: auto;\r\n}\r\n\r\n.catboost-graph {\r\n    margin-left: 290px;\r\n}\r\n\r\n.catboost-graph__tabs {\r\n    padding: 0 0 0 20px;\r\n}\r\n\r\n.catboost-graph__tab {\r\n    display: inline-block;\r\n    padding: 5px 10px 0 0;\r\n}\r\n\r\n.catboost-graph__tab {\r\n    color: #999;\r\n    cursor: pointer;\r\n    transition: color 0.1s linear;\r\n}\r\n\r\n.catboost-graph__tab:hover {\r\n    color: #333;\r\n}\r\n\r\n.catboost-graph__tab_active {\r\n    color: #000;\r\n    cursor: auto;\r\n}\r\n\r\n.catboost-graph__charts {\r\n    padding-top: 20px;\r\n}\r\n\r\n.catboost-graph__chart {\r\n    display: none;\r\n}\r\n\r\n.catboost-graph__chart_active {\r\n    display: block;\r\n}\r\n\r\n.catboost-panel__serie {\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid rgba(0, 0, 0, 0.3);\r\n    position: relative;\r\n}\r\n\r\n.catboost-panel__serie_bottom,\r\n.catboost-panel__serie_middle,\r\n.catboost-panel__serie_top {\r\n    white-space: nowrap;\r\n    position: relative;\r\n}\r\n\r\n#catboost-control-test {\r\n    margin-left: 11px;\r\n}\r\n\r\n.catboost-panel__serie_label {\r\n    padding: 0 0 0 8px;\r\n    width: 200px;\r\n    text-overflow: ellipsis;\r\n    box-sizing: border-box;\r\n    cursor: pointer;\r\n    margin-bottom: 0;\r\n    overflow: hidden;\r\n    position: relative;\r\n    top: 5px;\r\n}\r\n\r\n.catboost-panel__serie_hint {\r\n    position: absolute;\r\n    font-size: 9px;\r\n    left: 0;\r\n}\r\n\r\n.catboost-panel__serie__learn_hint {\r\n    top: 56px;\r\n}\r\n\r\n.catboost-panel__serie__test_hint {\r\n    top: 82px;\r\n}\r\n\r\n.catboost-panel__serie_bottom {\r\n    padding-bottom: 6px;\r\n}\r\n\r\n.catboost-panel__serie_time {\r\n    position: absolute;\r\n    top: 5px;\r\n    right: 2px;\r\n    height: 20px;\r\n    padding: 0 0 0 20px;\r\n    margin-bottom: 3px;\r\n    overflow: hidden;\r\n\r\n    text-overflow: ellipsis;\r\n    box-sizing: border-box;\r\n    text-align: left;\r\n}\r\n\r\n.catboost-panel__serie_learn_pic,\r\n.catboost-panel__serie_test_pic {\r\n    width: 13px;\r\n    height: 1px;\r\n    border-top-width: 1px;\r\n    position: relative;\r\n    top: -3px;\r\n    margin-right: 5px;\r\n}\r\n\r\n.catboost-panel__serie_learn_pic {\r\n    border-top-style: dashed;\r\n}\r\n\r\n.catboost-panel__serie_test_pic {\r\n    border-top-style: solid;\r\n}\r\n\r\n.catboost-panel__serie-value {\r\n    display: inline-block;\r\n    min-width: 30px;\r\n    margin-right: 2px;\r\n}\r\n\r\n.catboost-panel__controls_label .catboost-panel__serie_learn_pic {\r\n    padding-left: 4px;\r\n}\r\n\r\n.catboost-panel__serie_names {\r\n    white-space: nowrap;\r\n}\r\n\r\n.catboost-panel__serie_scroll {\r\n    width: 240px;\r\n    overflow-x: auto;\r\n    margin-left: 20px;\r\n}\r\n\r\n.catboost-panel__serie_learn_name,\r\n.catboost-panel__serie_test_name,\r\n.catboost-panel__serie_learn_value,\r\n.catboost-panel__serie_test_value,\r\n.catboost-panel__serie_best_learn_value,\r\n.catboost-panel__serie_best_test_value {\r\n    width: 85px;\r\n    position: relative;\r\n    padding: 0 8px 0 0;\r\n    box-sizing: content-box;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    top: 5px;\r\n}\r\n\r\n.catboost-panel__serie_iteration,\r\n.catboost-panel__serie_best_iteration {\r\n    display: inline-block;\r\n    position: absolute;\r\n    box-sizing: content-box;\r\n    overflow: hidden;\r\n    right: 2px;\r\n}\r\n\r\n.catboost-panel__serie_iteration {\r\n    top: 55px;\r\n}\r\n\r\n.catboost-panel__serie_best_iteration {\r\n    top: 80px;\r\n}\r\n\r\n.catboost-panel__control_slider {\r\n    width: 100px !important;\r\n    margin-left: 0;\r\n    position: relative;\r\n    display: inline-block !important;\r\n    top: 3px;\r\n}\r\n\r\n.catboost-panel__control_slidervalue {\r\n    width: 50px;\r\n    padding: 2px 3px;\r\n    margin-left: 4px;\r\n}\r\n\r\n.catboost-panel__serie_time_spend,\r\n.catboost-panel__serie_time_left {\r\n    display: inline-block;\r\n}\r\n\r\n.catboost-panel__serie_time_left {\r\n    margin-left: 10px;\r\n}\r\n\r\n.catboost-panel__serie_learn_pic,\r\n.catboost-panel__serie_learn_name,\r\n.catboost-panel__serie_learn_value,\r\n.catboost-panel__serie_best_learn_value {\r\n    display: inline-block;\r\n}\r\n.catboost-panel__serie_test_pic,\r\n.catboost-panel__serie_test_name,\r\n.catboost-panel__serie_test_value,\r\n.catboost-panel__serie_best_test_value {\r\n    display: inline-block;\r\n}\r\n\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_pic,\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_name,\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_value,\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_best_learn_value {\r\n    display: none;\r\n}\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_pic,\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_name,\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_value,\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_test_value {\r\n    display: none;\r\n}\r\n\r\n/*\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_test_value,\r\n.catboost-panel__series_learn_disabled .catboost-panel__serie_best_test_value {\r\n    width: 216px;\r\n}\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_learn_value,\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_learn_value {\r\n    width: 216px;\r\n}\r\n*/\r\n.catboost-panel__series_test_disabled .catboost-panel__serie__test_hint,\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_iteration {\r\n    display: none;\r\n}\r\n\r\n.catboost-panel__series_test_disabled.catboost-panel__series_learn_disabled .catboost-panel__serie_middle {\r\n    display: none;\r\n}\r\n\r\n.catboost-panel__series_test_disabled .catboost-panel__serie_bottom {\r\n    display: none;\r\n}\r\n",""]);const o=s},645:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var a=t(e);return e[2]?"@media ".concat(e[2]," {").concat(a,"}"):a})).join("")},e.i=function(t,a,r){"string"==typeof t&&(t=[[null,t,""]]);var s={};if(r)for(var o=0;o<this.length;o++){var n=this[o][0];null!=n&&(s[n]=!0)}for(var i=0;i<t.length;i++){var c=[].concat(t[i]);r&&s[c[0]]||(a&&(c[2]?c[2]="".concat(a," and ").concat(c[2]):c[2]=a),e.push(c))}},e}},78:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>n});var r=a(379),s=a.n(r),o=a(214);s()(o.Z,{insert:"head",singleton:!1});const n=o.Z.locals||{}},379:(t,e,a)=>{"use strict";var r,s=function(){var t={};return function(e){if(void 0===t[e]){var a=document.querySelector(e);if(window.HTMLIFrameElement&&a instanceof window.HTMLIFrameElement)try{a=a.contentDocument.head}catch(t){a=null}t[e]=a}return t[e]}}(),o=[];function n(t){for(var e=-1,a=0;a<o.length;a++)if(o[a].identifier===t){e=a;break}return e}function i(t,e){for(var a={},r=[],s=0;s<t.length;s++){var i=t[s],c=e.base?i[0]+e.base:i[0],l=a[c]||0,d="".concat(c," ").concat(l);a[c]=l+1;var p=n(d),h={css:i[1],media:i[2],sourceMap:i[3]};-1!==p?(o[p].references++,o[p].updater(h)):o.push({identifier:d,updater:v(h,e),references:1}),r.push(d)}return r}function c(t){var e=document.createElement("style"),r=t.attributes||{};if(void 0===r.nonce){var o=a.nc;o&&(r.nonce=o)}if(Object.keys(r).forEach((function(t){e.setAttribute(t,r[t])})),"function"==typeof t.insert)t.insert(e);else{var n=s(t.insert||"head");if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(e)}return e}var l,d=(l=[],function(t,e){return l[t]=e,l.filter(Boolean).join("\n")});function p(t,e,a,r){var s=a?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(t.styleSheet)t.styleSheet.cssText=d(e,s);else{var o=document.createTextNode(s),n=t.childNodes;n[e]&&t.removeChild(n[e]),n.length?t.insertBefore(o,n[e]):t.appendChild(o)}}function h(t,e,a){var r=a.css,s=a.media,o=a.sourceMap;if(s?t.setAttribute("media",s):t.removeAttribute("media"),o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}var _=null,b=0;function v(t,e){var a,r,s;if(e.singleton){var o=b++;a=_||(_=c(e)),r=p.bind(null,a,o,!1),s=p.bind(null,a,o,!0)}else a=c(e),r=h.bind(null,a,e),s=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(a)};return r(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;r(t=e)}else s()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=(void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r));var a=i(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var r=0;r<a.length;r++){var s=n(a[r]);o[s].references--}for(var c=i(t,e),l=0;l<a.length;l++){var d=n(a[l]);0===o[d].references&&(o[d].updater(),o.splice(d,1))}a=c}}}},294:(t,e,a)=>{a(78);var r=a(395),s=a(439),o=a(261),n=a(646),i=a(147).version;function c(){}c.prototype.init=function(){this.charts={},this.traces={},this.hovertextParameters=[],this.chartsToRedraw={},this.lastIndexes={},this.smoothness=-1,this.layoutDisabled={series:{},traces:{}},this.clickMode=!1,this.logarithmMode="linear",this.lastSmooth=0,this.layout=null,this.activeTab="",this.meta={},this.timeLeft={},this.hasCVMode=!1,this.stddevEnabled=!1,this.colors=["#68E256","#56AEE2","#CF56E2","#E28956","#56E289","#5668E2","#E256AE","#E2CF56","#56E2CF","#8A56E2","#E25668","#AEE256"],this.colorsByPath={},this.colorIndex=0,this.lossFuncs={},this.isCVinited=!1},c.prototype.loadStyles=function(t,e,a){n('link[catboost="1"]').remove();var r,s,o=document.getElementsByTagName("head")[0],i=document.createElement("link");i.setAttribute("href",t),i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.setAttribute("catboost","1"),"sheet"in i?(r="sheet",s="cssRules"):(r="styleSheet",s="rules");var c=setInterval((function(){try{i[r]&&i[r][s].length&&(clearInterval(c),clearTimeout(l),e.call(a||window,!0,i))}catch(t){}}),50),l=setTimeout((function(){clearInterval(c),clearTimeout(l),o.removeChild(i),e.call(a||window,!1,i)}),15e3);return o.appendChild(i),i},c.prototype.resizeCharts=function(){n(".catboost-graph__charts",this.layout).css({width:n(".catboost-graph").width()}),this.plotly.Plots.resize(this.traces[this.activeTab].parent)},c.prototype.addMeta=function(t,e){this.meta[t]=e},c.prototype.addLayout=function(t){if(!this.layout){var e="";this.hasCVMode&&(e='<div><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-cvstddev'+this.index+'"'+(this.stddevEnabled?' checked="checked"':"")+'></input><label for="catboost-control2-cvstddev'+this.index+'" class="catboost-panel__controls2_label catboost-panel__controls2_label-long">Standard Deviation</label></div>'),this.layout=n('<div class="catboost"><div class="catboost-panel"><div class="catboost-panel__controls"><input type="checkbox" class="catboost-panel__controls_checkbox" id="catboost-control-learn'+this.index+'" '+(this.layoutDisabled.learn?"":' checked="checked"')+'></input><label for="catboost-control-learn'+this.index+'" class="catboost-panel__controls_label"><div class="catboost-panel__serie_learn_pic" style="border-color:#999"></div>Learn</label><input type="checkbox" class="catboost-panel__controls_checkbox" id="catboost-control-test'+this.index+'" '+(this.layoutDisabled.test?"":' checked="checked"')+'></input><label for="catboost-control-test'+this.index+'" class="catboost-panel__controls_label"><div class="catboost-panel__serie_test_pic" style="border-color:#999"></div>Eval</label></div><div class="catboost-panel__series '+(this.layoutDisabled.learn?" catboost-panel__series_learn_disabled":"")+'"></div><div class="catboost-panel__controls2"><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-clickmode'+this.index+'"></input><label for="catboost-control2-clickmode'+this.index+'" class="catboost-panel__controls2_label">Click Mode</label><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-log'+this.index+'"></input><label for="catboost-control2-log'+this.index+'" class="catboost-panel__controls2_label">Logarithm</label><div><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-smooth'+this.index+'"></input><label for="catboost-control2-smooth'+this.index+'" class="catboost-panel__controls2_label">Smooth</label><input id="catboost-control2-slider'+this.index+'" disabled="disabled" class="catboost-panel__control_slider" type ="range" value="0" min="0" max="1" step ="0.01" for="rangeInputValue" name="rangeInput"/><input id="catboost-control2-slidervalue'+this.index+'" disabled="disabled" class="catboost-panel__control_slidervalue" value="0" min="0" max="1" for="rangeInput" name="rangeInputValue"/></div>'+e+'</div></div><div class="catboost-graph"><div class="catboost-graph__tabs"></div><div class="catboost-graph__charts"></div></div></div>'),n(t).append(this.layout),this.addTabEvents(),this.addControlEvents()}},c.prototype.addTabEvents=function(){var t=this;n(".catboost-graph__tabs",this.layout).click((function(e){if(n(e.target).is(".catboost-graph__tab:not(.catboost-graph__tab_active)")){var a=n(e.target).attr("tabid");t.activeTab=a,n(".catboost-graph__tab_active",t.layout).removeClass("catboost-graph__tab_active"),n(".catboost-graph__chart_active",t.layout).removeClass("catboost-graph__chart_active"),n('.catboost-graph__tab[tabid="'+a+'"]',t.layout).addClass("catboost-graph__tab_active"),n('.catboost-graph__chart[tabid="'+a+'"]',t.layout).addClass("catboost-graph__chart_active"),t.cleanSeries(),t.redrawActiveChart(),t.resizeCharts()}}))},c.prototype.addControlEvents=function(){var t=this;n("#catboost-control-learn"+this.index,this.layout).click((function(){t.layoutDisabled.learn=!n(this)[0].checked,n(".catboost-panel__series",t.layout).toggleClass("catboost-panel__series_learn_disabled",t.layoutDisabled.learn),t.redrawActiveChart()})),n("#catboost-control-test"+this.index,this.layout).click((function(){t.layoutDisabled.test=!n(this)[0].checked,n(".catboost-panel__series",t.layout).toggleClass("catboost-panel__series_test_disabled",t.layoutDisabled.test),t.redrawActiveChart()})),n("#catboost-control2-clickmode"+this.index,this.layout).click((function(){t.clickMode=n(this)[0].checked})),n("#catboost-control2-log"+this.index,this.layout).click((function(){t.logarithmMode=n(this)[0].checked?"log":"linear",t.forEveryLayout((function(e){e.yaxis={type:t.logarithmMode}})),t.redrawActiveChart()}));var e=n("#catboost-control2-slider"+this.index),a=n("#catboost-control2-slidervalue"+this.index);n("#catboost-control2-smooth"+this.index,this.layout).click((function(){var r=n(this)[0].checked;t.setSmoothness(r?t.lastSmooth:-1),e.prop("disabled",!r),a.prop("disabled",!r),t.redrawActiveChart()})),n("#catboost-control2-cvstddev"+this.index,this.layout).click((function(){var e=n(this)[0].checked;t.setStddev(e),t.redrawActiveChart()})),e.on("input change",(function(){var e=Number(n(this).val());a.val(isNaN(e)?0:e),t.setSmoothness(e),t.lastSmooth=e,t.redrawActiveChart()})),a.on("input change",(function(){var a=Number(n(this).val());e.val(isNaN(a)?0:a),t.setSmoothness(a),t.lastSmooth=a,t.redrawActiveChart()}))},c.prototype.setTraceVisibility=function(t,e){t&&(t.visible=e)},c.prototype.updateTracesVisibility=function(){var t,e=this.groupTraces(),a=-1===this.getSmoothness(),r=this;for(var s in e)e.hasOwnProperty(s)&&(t=e[s].traces,this.layoutDisabled.traces[s]?t.forEach((function(t){r.setTraceVisibility(t,!1)})):(t.forEach((function(t){r.setTraceVisibility(t,!0)})),this.hasCVMode&&(this.stddevEnabled?(r.filterTracesOne(t,{type:"learn"}).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesOne(t,{type:"test"}).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0})).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0})).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0,smoothed:!0})).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,smoothed:!0})).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,best_point:!0})).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesOne(t,{cv_stddev_first:!0}).forEach((function(t){r.setTraceVisibility(t,!0)})),r.filterTracesOne(t,{cv_stddev_last:!0}).forEach((function(t){r.setTraceVisibility(t,!0)}))):(r.filterTracesOne(t,{cv_stddev_first:!0}).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesOne(t,{cv_stddev_last:!0}).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0})).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0})).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0,smoothed:!0})).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,smoothed:!0})).forEach((function(t){r.setTraceVisibility(t,!1)})),r.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,best_point:!0})).forEach((function(t){r.setTraceVisibility(t,!1)})))),a&&r.filterTracesOne(t,{smoothed:!0}).forEach((function(t){r.setTraceVisibility(t,!1)})),this.layoutDisabled.learn&&r.filterTracesOne(t,{type:"learn"}).forEach((function(t){r.setTraceVisibility(t,!1)})),this.layoutDisabled.test&&r.filterTracesOne(t,{type:"test"}).forEach((function(t){r.setTraceVisibility(t,!1)}))))},c.prototype.getSmoothness=function(){return this.smoothness&&this.smoothness>-1?this.smoothness:-1},c.prototype.setSmoothness=function(t){t<0&&-1!==t||t>1||(this.smoothness=t)},c.prototype.setStddev=function(t){this.stddevEnabled=t},c.prototype.redrawActiveChart=function(){this.chartsToRedraw[this.activeTab]=!0,this.redrawAll()},c.prototype.redraw=function(){this.chartsToRedraw[this.activeTab]&&(this.chartsToRedraw[this.activeTab]=!1,this.updateTracesVisibility(),this.updateTracesCV(),this.updateTracesBest(),this.updateTracesValues(),this.updateTracesSmoothness(),this.plotly.redraw(this.traces[this.activeTab].parent)),this.drawTraces()},c.prototype.addRedrawFunc=function(){this.redrawFunc=function(t,e,a,r){var s=typeof a;"undefined"===s?a=!0:3===arguments.length&&"boolean"!==s&&(r=a,a=!0);var o,n,i,c=function(){i?(t.apply(r,n),i=!1,o=setTimeout(c,e)):o=null};return function(){n=arguments,r||(r=this),i=!0,o||(a?c():o=setTimeout(c,e))}}(this.redraw,400,!1,this)},c.prototype.redrawAll=function(){this.redrawFunc||this.addRedrawFunc(),this.redrawFunc()},c.prototype.addPoints=function(t,e){var a=this;e.chunks.forEach((function(r){void 0!==r.remaining_time&&void 0!==r.passed_time&&(a.timeLeft[e.path]||(a.timeLeft[e.path]=[]),a.timeLeft[e.path][r.iteration]=[r.remaining_time,r.passed_time]),["test","learn"].forEach((function(s){for(var o=a.meta[e.path][s+"_sets"],i=a.meta[e.path][s+"_metrics"],c=0;c<i.length;c++){var l=i[c].name,d=!1;hovertextParametersAdded=!1,a.lossFuncs[l]=i[c].best_value;for(var p=0;p<o.length;p++){var h=o[p],_={chartName:l,index:c,train:e.train,type:s,path:e.path,indexOfSet:p,nameOfSet:h},b=a.getKey(_),v=a.getLaunchMode(e.path);a.activeTab||(a.activeTab=b.chartId),"CV"===v&&(a.hasCVMode=!0,a.isCVinited||(a.layoutDisabled.learn=!0,a.setStddev(!0),a.isCVinited=!0));var u=r[h],f=u[c],m=r.iteration,y=a.getTrace(t,_),g=a.getTrace(t,n.extend({smoothed:!0},_)),x=null;if("test"===s&&("CV"!==v&&a.getTrace(t,n.extend({best_point:!0},_)),"number"==typeof a.lossFuncs[l]&&(x=a.getTrace(t,n.extend({best_value:!0},_)))),"inf"!==f&&"nan"!==f){if(y.x[m]=m,y.y[m]=u[c],y.hovertext[m]=h+": "+u[c].toPrecision(7),r.hasOwnProperty("parameters")){for(var T in a.hovertextParameters[m]="",r.parameters[0])r.parameters[0].hasOwnProperty(T)&&(valueOfParameter=r.parameters[0][T],a.hovertextParameters[m]+="<br>"+T+" : "+valueOfParameter);hovertextParametersAdded||"test"!==s||(hovertextParametersAdded=!0,y.hovertext[m]+=a.hovertextParameters[m])}g.x[m]=m}x&&(x.x[m]=m,x.y[m]=a.lossFuncs[l]),"CV"!==v||d||(d=!0,a.getTrace(t,n.extend({cv_stddev_first:!0},_)),a.getTrace(t,n.extend({cv_stddev_last:!0},_)),a.getTrace(t,n.extend({cv_stddev_first:!0,smoothed:!0},_)),a.getTrace(t,n.extend({cv_stddev_last:!0,smoothed:!0},_)),a.getTrace(t,n.extend({cv_avg:!0},_)),a.getTrace(t,n.extend({cv_avg:!0,smoothed:!0},_)),"test"===s&&a.getTrace(t,n.extend({cv_avg:!0,best_point:!0},_)))}a.chartsToRedraw[b.chartId]=!0,a.redrawAll()}}))}))},c.prototype.getLaunchMode=function(t){return this.meta[t].launch_mode},c.prototype.getChartNode=function(t,e){var a=n('<div class="catboost-graph__chart" tabid="'+t.id+'"></div>');return e&&a.addClass("catboost-graph__chart_active"),a},c.prototype.getChartTab=function(t,e){var a=n('<div class="catboost-graph__tab" tabid="'+t.id+'">'+t.name+"</div>");return e&&a.addClass("catboost-graph__tab_active"),a},c.prototype.forEveryChart=function(t){for(var e in this.traces)this.traces.hasOwnProperty(e)&&t(this.traces[e])},c.prototype.forEveryLayout=function(t){this.forEveryChart((function(e){t(e.layout)}))},c.prototype.getChart=function(t,e){var a=e.id,r=this;if(this.charts[a])return this.charts[a];this.addLayout(t);var s=this.activeTab===e.id,o=this.getChartNode(e,s),i=this.getChartTab(e,s);return n(".catboost-graph__charts",this.layout).append(o),n(".catboost-graph__tabs",this.layout).append(i),this.traces[a]={id:e.id,name:e.name,parent:o[0],traces:[],layout:{xaxis:{range:[0,Number(this.meta[e.path].iteration_count)],type:"linear",tickmode:"auto",showspikes:!0,spikethickness:1,spikedash:"longdashdot",spikemode:"across",zeroline:!1,showgrid:!1},yaxis:{zeroline:!1},separators:". ",margin:{l:38,r:0,t:35,b:30},autosize:!0,showlegend:!1},options:{scrollZoom:!1,modeBarButtonsToRemove:["toggleSpikelines"],displaylogo:!1}},this.charts[a]=this.plotly.plot(o[0],this.traces[a].traces,this.traces[a].layout,this.traces[a].options),o[0].on("plotly_hover",(function(t){r.updateTracesValues(t.points[0].x)})),o[0].on("plotly_click",(function(t){r.updateTracesValues(t.points[0].x,!0)})),this.charts[a]},c.prototype.getTrace=function(t,e){var a=this.getKey(e),r=[];if(this.traces[a.chartId]&&(r=this.traces[a.chartId].traces.filter((function(t){return t.name===a.traceName}))),r.length)return r[0];this.getChart(t,{id:a.chartId,name:e.chartName,path:e.path});var s={color:this.getNextColor(e.path,e.smoothed?.2:1),fillsmoothcolor:this.getNextColor(e.path,.1),fillcolor:this.getNextColor(e.path,.4),hoverinfo:e.cv_avg?"skip":"text+x",width:e.cv_avg?2:1,dash:"test"===e.type?"solid":"dot"},o={name:a.traceName,_params:e,x:[],y:[],hovertext:[],hoverinfo:s.hoverinfo,line:{width:s.width,dash:s.dash,color:s.color},mode:"lines",hoveron:"points",connectgaps:!0};return e.best_point&&(o={name:a.traceName,_params:e,x:[],y:[],marker:{width:2,color:s.color},hovertext:[],hoverinfo:"text",mode:"markers",type:"scatter"}),e.best_value&&(o={name:a.traceName,_params:e,x:[],y:[],line:{width:1,dash:"dash",color:"#CCCCCC"},mode:"lines",connectgaps:!0,hoverinfo:"skip"}),e.cv_stddev_last&&(o.fill="tonexty"),o._params.plotParams=s,this.traces[a.chartId].traces.push(o),o},c.prototype.getKey=function(t){var e=[t.train,t.type,t.indexOfSet,t.smoothed?"smoothed":"",t.best_point?"best_pount":"",t.best_value?"best_value":"",t.cv_avg?"cv_avg":"",t.cv_stddev_first?"cv_stddev_first":"",t.cv_stddev_last?"cv_stddev_last":""].join(";");return{chartId:t.chartName,traceName:e,colorId:t.train}},c.prototype.filterTracesEvery=function(t,e){return(t=t||this.traces[this.activeTab].traces).filter((function(t){for(var a in e)if(e.hasOwnProperty(a)&&e[a]!==t._params[a])return!1;return!0}))},c.prototype.filterTracesOne=function(t,e){return(t=t||this.traces[this.activeTab].traces).filter((function(t){for(var a in e)if(e.hasOwnProperty(a)&&e[a]===t._params[a])return!0;return!1}))},c.prototype.cleanSeries=function(){n(".catboost-panel__series",this.layout).html("")},c.prototype.groupTraces=function(){var t=this.traces[this.activeTab].traces,e=0,a={};return t.map((function(t){var r=t._params.train;a[r]||(a[r]={index:e,traces:[],info:{path:t._params.path,color:t._params.plotParams.color}},e++),a[r].traces.push(t)})),a},c.prototype.drawTraces=function(){var t="",e=this.groupTraces(),a=n(".catboost-panel__series .catboost-panel__serie",this.layout).length;if(Object.keys(e).filter(hasOwnProperty.bind(e)).length!==a){for(var r in e)e.hasOwnProperty(r)&&(t+=this.drawTrace(r,e[r]));n(".catboost-panel__series",this.layout).html(t),this.updateTracesValues(),this.addTracesEvents()}},c.prototype.getTraceDefParams=function(t){var e={smoothed:void 0,best_point:void 0,best_value:void 0,cv_avg:void 0,cv_stddev_first:void 0,cv_stddev_last:void 0};return t?n.extend(e,t):e},c.prototype.drawTrace=function(t,e){var a=e.info,r="catboost-serie-"+this.index+"-"+e.index,s={learn:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"learn"})),test:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test"}))},o={learn:{middle:"",bottom:""},test:{middle:"",bottom:""}},n="";return["learn","test"].forEach((function(t){s[t].forEach((function(e){o[t].middle+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:'+a.color+'"></div><div data-index="'+e._params.indexOfSet+'" class="catboost-panel__serie_'+t+'_value"></div>',o[t].bottom+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:transparent"></div><div data-index="'+e._params.indexOfSet+'" class="catboost-panel__serie_best_'+t+'_value"></div>',n+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:'+a.color+'"></div><div class="catboost-panel__serie_'+t+'_name">'+e._params.nameOfSet+"</div>"}))})),'<div id="'+r+'" class="catboost-panel__serie" style="color:'+a.color+'"><div class="catboost-panel__serie_top"><input type="checkbox" data-seriename="'+t+'" class="catboost-panel__serie_checkbox" id="'+r+'-box" '+(this.layoutDisabled.series[t]?"":'checked="checked"')+"></input><label title="+this.meta[a.path].name+' for="'+r+'-box" class="catboost-panel__serie_label">'+t+'<div class="catboost-panel__serie_time_left" title="Estimate time"></div></label>'+("Eval"!==this.getLaunchMode(a.path)?'<div class="catboost-panel__serie_time"><div class="catboost-panel__serie_time_spend" title="Time spend"></div></div>':"")+'</div><div class="catboost-panel__serie_hint catboost-panel__serie__learn_hint">curr</div><div class="catboost-panel__serie_hint catboost-panel__serie__test_hint">best</div><div class="catboost-panel__serie_iteration" title="curr iteration"></div><div class="catboost-panel__serie_best_iteration" title="best '+(this.hasCVMode?"avg ":"")+'iteration"></div><div class="catboost-panel__serie_scroll"><div class="catboost-panel__serie_names">'+n+'</div><div class="catboost-panel__serie_middle">'+o.learn.middle+o.test.middle+'</div><div class="catboost-panel__serie_bottom">'+o.learn.bottom+o.test.bottom+"</div></div></div>"},c.prototype.updateTracesValues=function(t,e){var a=this.groupTraces();for(var r in a)a.hasOwnProperty(r)&&!this.layoutDisabled.traces[r]&&this.updateTraceValues(r,a[r],t,e)},c.prototype.updateTracesBest=function(){var t=this.groupTraces();for(var e in t)t.hasOwnProperty(e)&&!this.layoutDisabled.traces[e]&&this.updateTraceBest(e,t[e])},c.prototype.getBestValue=function(t){if(!t.length)return{best:void 0,index:-1};for(var e=t[0],a=0,r=this.lossFuncs[this.traces[this.activeTab].name],s="number"==typeof r?Math.abs(t[0]-r):0,o=1,n=t.length;o<n;o++)"Min"===r&&t[o]<e&&(e=t[o],a=o),"Max"===r&&t[o]>e&&(e=t[o],a=o),"number"==typeof r&&Math.abs(t[o]-r)<s&&(e=t[o],s=Math.abs(t[o]-r),a=o);return{best:e,index:a,func:r}},c.prototype.updateTracesCV=function(){this.updateTracesCVAvg(),this.hasCVMode&&this.stddevEnabled&&this.updateTracesCVStdDev()},c.prototype.updateTracesCVAvg=function(){var t=this.groupTraces(),e=this.filterTracesEvery(t.traces,this.getTraceDefParams({cv_avg:!0})),a=this;e.forEach((function(e){var r=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed}));r.length&&a.cvAvgFunc(r,e)}))},c.prototype.cvAvgFunc=function(t,e){var a,r,s=t.length,o=-1;t.forEach((function(t){t.y.length>o&&(o=t.y.length)}));for(var n=0;n<o;n++){r=0,a=0;for(var i=0;i<s;i++)void 0!==t[i].y[n]&&(r+=t[i].y[n],a++);a>0&&(e.x[n]=n,e.y[n]=r/a)}},c.prototype.updateTracesCVStdDev=function(){var t=this.groupTraces(),e=this.filterTracesOne(t.traces,{cv_stddev_first:!0}),a=this;e.forEach((function(e){var r=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed})),s=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed,cv_stddev_last:!0}));r.length&&1===s.length&&a.cvStdDevFunc(r,e,s[0])}))},c.prototype.cvStdDevFunc=function(t,e,a){var r,s,o,n,i=t.length,c=-1;for(t.forEach((function(t){t.y.length>c&&(c=t.y.length)})),o=0;o<c;o++){for(s=0,r=0,n=0;n<i;n++)void 0!==t[n].y[o]&&(s+=t[n].y[o],r++);if(!(r<=0)){var l=0,d=s/r;for(n=0;n<i;n++)void 0!==t[n].y[o]&&(l+=Math.pow(t[n].y[o]-d,2));l/=r-1,l=Math.pow(l,.5),e.x[o]=o,e.y[o]=d-l,e.hovertext[o]=e._params.type+" std: "+d.toFixed(7)+"-"+l.toFixed(7),a.x[o]=o,a.y[o]=d+l,a.hovertext[o]=a._params.type+" std: "+d.toFixed(7)+"+"+l.toFixed(7),this.hovertextParameters.length>o&&(e.hovertext[o]+=this.hovertextParameters[o],a.hovertext[o]+=this.hovertextParameters[o])}}},c.prototype.updateTracesSmoothness=function(){var t=this.groupTraces(),e=this.filterTracesOne(t.traces,{smoothed:!0}),a=this.getSmoothness()>-1,r=this;e.forEach((function(e){var s=r.filterTracesEvery(t.traces,r.getTraceDefParams({train:e._params.train,type:e._params.type,indexOfSet:e._params.indexOfSet,cv_avg:e._params.cv_avg,cv_stddev_first:e._params.cv_stddev_first,cv_stddev_last:e._params.cv_stddev_last})),o=!1;1===s.length&&(s=s[0]).visible&&(a&&(r.smoothFunc(s,e),o=!0),r.highlightSmoothedTrace(s,e,o))}))},c.prototype.highlightSmoothedTrace=function(t,e,a){a?(e.line.color=t._params.plotParams.color,t.line.color=e._params.plotParams.color,t.hoverinfo="skip",t._params.cv_stddev_last&&(t.fillcolor=t._params.plotParams.fillsmoothcolor)):(t.line.color=t._params.plotParams.color,t.hoverinfo=t._params.plotParams.hoverinfo,t._params.cv_stddev_last&&(t.fillcolor=t._params.plotParams.fillcolor))},c.prototype.smoothFunc=function(t,e){var a=t.y,r=this.smooth(a,this.getSmoothness()),s=0,o=this;r.length&&a.forEach((function(t,a){e.x[a]||(e.x[a]=a);var n=e._params.nameOfSet;(e._params.cv_stddev_first||e._params.cv_stddev_last)&&(n=e._params.type+" std"),e.y[a]=r[s],e.hovertext[a]=n+"`: "+r[s].toPrecision(7),o.hovertextParameters.length>a&&(e.hovertext[a]+=o.hovertextParameters[a]),s++}))},c.prototype.formatItemValue=function(t,e,a){return void 0===t?"":'<span title="'+(a=a||"")+"value "+t+'">'+t+"</span>"},c.prototype.updateTraceBest=function(t,e){var a=this.filterTracesOne(e.traces,{best_point:!0}),r=this;a.forEach((function(t){var a=r.filterTracesEvery(e.traces,r.getTraceDefParams({train:t._params.train,type:"test",indexOfSet:t._params.indexOfSet}));r.hasCVMode&&(a=r.filterTracesEvery(e.traces,r.getTraceDefParams({train:t._params.train,type:"test",cv_avg:!0})));var s=r.getBestValue(1===a.length?a[0].y:[]);-1!==s.index&&(t.x[0]=s.index,t.y[0]=s.best,t.hovertext[0]=s.func+" ("+(r.hasCVMode?"avg":t._params.nameOfSet)+"): "+s.index+" "+s.best)}))},c.prototype.updateTraceValues=function(t,e,a,r){var s="catboost-serie-"+this.index+"-"+e.index,o={learn:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"learn"})),test:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test"}))},i=e.info.path,c=this;if(["learn","test"].forEach((function(t){o[t].forEach((function(o){var l=o.y||[],d=void 0!==a&&a<l.length-1?a:l.length-1,p=l.length?l[d]:void 0,h=c.filterTracesEvery(e.traces,c.getTraceDefParams({type:"test",indexOfSet:o._params.indexOfSet})),_=c.getBestValue(1===h.length?h[0].y:[]),b="",v="";!r&&c.clickMode||(n("#"+s+" .catboost-panel__serie_"+t+"_value[data-index="+o._params.indexOfSet+"]",c.layout).html(c.formatItemValue(p,d,t+" ")),n("#"+s+" .catboost-panel__serie_iteration",c.layout).html(d),c.timeLeft[i]&&c.timeLeft[i][l.length-1]&&(b=c.timeLeft[i][l.length-1][0]),n("#"+s+" .catboost-panel__serie_time_left",c.layout).html(b?"~"+c.convertTime(b):""),c.timeLeft[i]&&c.timeLeft[i][d]&&(v=c.timeLeft[i][d][1]),n("#"+s+" .catboost-panel__serie_time_spend",c.layout).html(c.convertTime(v)),n("#"+s+" .catboost-panel__serie_best_iteration",c.layout).html(_.index>-1?_.index:""),n("#"+s+" .catboost-panel__serie_best_test_value[data-index="+o._params.indexOfSet+"]",c.layout).html(c.formatItemValue(_.best,_.index,"best "+o._params.nameOfSet+" ")))}))})),this.hasCVMode){var l=this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test",cv_avg:!0})),d=this.getBestValue(1===l.length?l[0].y:[]);n("#"+s+" .catboost-panel__serie_best_iteration",this.layout).html(d.index>-1?d.index:"")}r&&(this.clickMode=!0,n("#catboost-control2-clickmode"+this.index,this.layout)[0].checked=!0)},c.prototype.addTracesEvents=function(){var t=this;n(".catboost-panel__serie_checkbox",this.layout).click((function(){var e=n(this).data("seriename");t.layoutDisabled.traces[e]=!n(this)[0].checked,t.redrawActiveChart()}))},c.prototype.getNextColor=function(t,e){var a;return this.colorsByPath[t]?a=this.colorsByPath[t]:(a=this.colors[this.colorIndex],this.colorsByPath[t]=a,this.colorIndex++,this.colorIndex>this.colors.length-1&&(this.colorIndex=0)),this.hexToRgba(a,e)},c.prototype.hexToRgba=function(t,e){t.length<6&&(t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])/i,(function(t,e,a,r){return"#"+e+e+a+a+r+r})));var a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i.exec(t);return"rgba("+parseInt(a[1],16)+","+parseInt(a[2],16)+","+parseInt(a[3],16)+","+e+")"},c.prototype.convertTime=function(t){if(!t)return"0s";var e=(t=Math.floor(1e3*t))%1e3,a=(t=parseInt(t/1e3,10))%60,r=(t=parseInt(t/60,10))%60,s=(t=parseInt(t/60,10))%24,o="";return s&&s>0&&(o+=s+"h ",a=0,e=0),r&&r>0&&(o+=r+"m ",e=0),a&&a>0&&(o+=a+"s "),e&&e>0&&(o+=e+"ms"),o.trim()},c.prototype.mean=function(t,e){var a,r=t.length,s=r,o=-1,n=0,i=function(t){return null===t?NaN:+t};if(null===e)for(;++o<r;)isNaN(a=i(t[o]))?--s:n+=a;else for(;++o<r;)isNaN(a=i(e(t[o],o,t)))?--s:n+=a;if(s)return n/s},c.prototype.smooth=function(t,e){var a=(Math.pow(1e3,e)-1)/999,r=Math.floor(t.length*a/2),s=[],o=this;return t.forEach((function(e,a){var n=Math.min(r,a,t.length-a-1),i=a-n,c=a+n+1,l=e;isFinite(l)?s.push(o.mean(t.slice(i,c).filter((function(t){return isFinite(t)})),null)):s.push(l)})),s};var l=function(t){var e=n(t).attr("catboost-id");return e?(e=e.replace("catboost_",""),window.catboostIpythonInstances[e]?window.catboostIpythonInstances[e]:null):null};class d extends r.DOMWidgetView{initialize(){r.DOMWidgetView.prototype.initialize.apply(this,arguments),window.catboostIpythonInstances||(window.catboostIpythonInstances={}),void 0===window.catboostIpythonIndex&&(window.catboostIpythonIndex=0);var t=l(this.el);t||(t=function(t){n(t).attr("catboost-id","catboost_"+window.catboostIpythonIndex);var e=new c;return e.index=catboostIpythonIndex,e.plotly=o,window.catboostIpythonInstances[window.catboostIpythonIndex]=e,window.catboostIpythonIndex++,e}(this.el)),t.init()}render(){this.value_changed(),this.model.on("change:value",this.value_changed,this)}update(){this.value_changed()}value_changed(){this.el.style.width=this.model.get("width"),this.el.style.height=this.model.get("height"),this.displayed.then(s.bind(this.render_charts,this))}process_all(t,e){var a=e.data;for(var r in a)a.hasOwnProperty(r)&&this.process_row(t,a[r])}process_row(t,e){var a=l(t),r=e.path,s=e.content,o=s.data.iterations,n=0,i=[];if(o&&o.length){a.lastIndex||(a.lastIndex={}),a.lastIndex[r]&&(n=a.lastIndex[r]+1),a.lastIndex[r]=o.length-1;for(var c=n;c<o.length;c++)i.push(o[c]);a.addMeta(e.path,s.data.meta),a.addPoints(t,{chunks:i,train:e.name,path:e.path})}}render_charts(){return this.process_all(this.el,{data:this.model.get("data")}),this}}class p extends r.DOMWidgetModel{defaults(){return Object.assign({},super.defaults(),{_model_name:"CatboostWidgetModel",_view_name:"CatboostWidgetView",_model_module:"catboost-widget",_view_module:"catboost-widget",_model_module_version:i,_view_module_version:i})}}t.exports={CatboostWidgetModel:p,CatboostWidgetView:d}},138:(t,e,a)=>{t.exports=a(294),t.exports.version=a(147).version},147:t=>{"use strict";t.exports={version:"1.2.8"}}}]);