"""
إعدادات قواعد البيانات
PostgreSQL و Redis
"""

import asyncio
from typing import Optional, Dict, Any
import psycopg2
from psycopg2.extras import RealDictCursor
import redis
import asyncpg
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from .settings import settings

class DatabaseConfig:
    """فئة إعدادات قواعد البيانات"""
    
    def __init__(self):
        self.postgres_engine = None
        self.postgres_session = None
        self.redis_client = None
        self.async_postgres_pool = None
    
    # ==========================================
    # 🔵 PostgreSQL - الاتصال المتزامن
    # ==========================================
    def get_postgres_connection(self):
        """الحصول على اتصال PostgreSQL متزامن"""
        try:
            conn = psycopg2.connect(
                host=settings.DB_HOST,
                port=settings.DB_PORT,
                database=settings.DB_NAME,
                user=settings.DB_USER,
                password=settings.DB_PASSWORD,
                cursor_factory=RealDictCursor
            )
            return conn
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ PostgreSQL: {e}")
            raise
    
    def get_postgres_engine(self):
        """الحصول على محرك SQLAlchemy"""
        if not self.postgres_engine:
            self.postgres_engine = create_engine(
                settings.DATABASE_URL,
                echo=False,
                pool_size=10,
                max_overflow=20
            )
        return self.postgres_engine
    
    def get_postgres_session(self):
        """الحصول على جلسة SQLAlchemy"""
        if not self.postgres_session:
            engine = self.get_postgres_engine()
            Session = sessionmaker(bind=engine)
            self.postgres_session = Session()
        return self.postgres_session
    
    # ==========================================
    # 🔵 PostgreSQL - الاتصال غير المتزامن
    # ==========================================
    async def get_async_postgres_pool(self):
        """الحصول على pool اتصالات PostgreSQL غير متزامن"""
        if not self.async_postgres_pool:
            try:
                self.async_postgres_pool = await asyncpg.create_pool(
                    host=settings.DB_HOST,
                    port=settings.DB_PORT,
                    database=settings.DB_NAME,
                    user=settings.DB_USER,
                    password=settings.DB_PASSWORD,
                    min_size=5,
                    max_size=20
                )
                print("✅ تم إنشاء pool اتصالات PostgreSQL غير المتزامن")
            except Exception as e:
                print(f"❌ خطأ في إنشاء pool PostgreSQL: {e}")
                raise
        return self.async_postgres_pool
    
    async def execute_async_query(self, query: str, *args):
        """تنفيذ استعلام غير متزامن"""
        pool = await self.get_async_postgres_pool()
        async with pool.acquire() as conn:
            return await conn.fetch(query, *args)
    
    async def execute_async_command(self, command: str, *args):
        """تنفيذ أمر غير متزامن (INSERT, UPDATE, DELETE)"""
        pool = await self.get_async_postgres_pool()
        async with pool.acquire() as conn:
            return await conn.execute(command, *args)
    
    # ==========================================
    # 🔴 Redis - الاتصال المتزامن وغير المتزامن
    # ==========================================
    def get_redis_client(self):
        """الحصول على عميل Redis"""
        if not self.redis_client:
            try:
                self.redis_client = redis.Redis(**settings.REDIS_CONFIG)
                # اختبار الاتصال
                self.redis_client.ping()
                print("✅ تم الاتصال بـ Redis بنجاح")
            except Exception as e:
                print(f"❌ خطأ في الاتصال بـ Redis: {e}")
                raise
        return self.redis_client
    
    # ==========================================
    # 🔧 دوال مساعدة
    # ==========================================
    def test_connections(self) -> Dict[str, bool]:
        """اختبار جميع الاتصالات"""
        results = {
            'postgresql': False,
            'redis': False
        }
        
        # اختبار PostgreSQL
        try:
            conn = self.get_postgres_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            results['postgresql'] = True
            print("✅ PostgreSQL متصل")
        except Exception as e:
            print(f"❌ PostgreSQL غير متصل: {e}")
        
        # اختبار Redis
        try:
            redis_client = self.get_redis_client()
            redis_client.ping()
            results['redis'] = True
            print("✅ Redis متصل")
        except Exception as e:
            print(f"❌ Redis غير متصل: {e}")
        
        return results
    
    async def test_async_connections(self) -> Dict[str, bool]:
        """اختبار الاتصالات غير المتزامنة"""
        results = {
            'async_postgresql': False
        }
        
        # اختبار PostgreSQL غير المتزامن
        try:
            pool = await self.get_async_postgres_pool()
            async with pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    results['async_postgresql'] = True
                    print("✅ PostgreSQL غير المتزامن متصل")
        except Exception as e:
            print(f"❌ PostgreSQL غير المتزامن غير متصل: {e}")
        
        return results
    
    def close_connections(self):
        """إغلاق جميع الاتصالات"""
        if self.postgres_session:
            self.postgres_session.close()
        
        if self.postgres_engine:
            self.postgres_engine.dispose()
        
        if self.redis_client:
            self.redis_client.close()
        
        print("✅ تم إغلاق جميع الاتصالات")
    
    async def close_async_connections(self):
        """إغلاق الاتصالات غير المتزامنة"""
        if self.async_postgres_pool:
            await self.async_postgres_pool.close()
            print("✅ تم إغلاق pool PostgreSQL غير المتزامن")

# إنشاء مثيل إعدادات قاعدة البيانات
db_config = DatabaseConfig()

# قاعدة SQLAlchemy
Base = declarative_base()

# دالة للحصول على جلسة قاعدة البيانات
def get_db_session():
    """الحصول على جلسة قاعدة البيانات"""
    return db_config.get_postgres_session()

# دالة للحصول على عميل Redis
def get_redis():
    """الحصول على عميل Redis"""
    return db_config.get_redis_client()
